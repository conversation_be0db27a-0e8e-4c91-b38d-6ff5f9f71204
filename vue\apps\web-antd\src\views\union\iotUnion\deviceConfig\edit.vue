<template>
  <Modal :close-on-click-modal="false" centered title="选择设备" class="w-[700px]">
    <Page auto-content-height>
      <Grid>
        <template #deviceState="{ row }">
          {{ getDeviceStateLabel(row.deviceState) }}
        </template>
        <template #status="{ row }">
          {{ row.status == 0 ? "启用" : "未启用" }}
        </template>
      </Grid>
    </Page>
  </Modal>
</template>
<script lang="ts" setup>
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type{  VbenFormProps } from '#/adapter/form';
import { detailColumns, detailSchema } from './model';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { List } from '#/api/device/iotDevice';
import { ListNoPage as ListProduct } from '#/api/device/iotProduct';
import { ref } from 'vue';

const emit = defineEmits(['confirm', 'reload']);
/** 设备状态 */
const deviceStateOptions = getDictOptions(DictEnum.DEVICE_STATE); // 设备状态字典
function getDeviceStateLabel(value: string | number) {
  const found = deviceStateOptions.find((opt: any) => String(opt.value) === String(value));
  return found ? found.label : value;
}
/** 模态框实例 */
const [Modal,modalApi] = useVbenModal({
  onCancel: handleCancel,
    onConfirm: handleConfirm,
    async onOpenChange(isOpen) {
        if (!isOpen) {
            return null;
        }
        modalApi.setState({ confirmLoading: true, loading: true });
        await loadProductOptions();
        modalApi.setState({ confirmLoading: false, loading: false });
        modalApi.setState({ showConfirmButton: true });
    }
});

function handleCancel() {
    modalApi.close();
}
function handleConfirm() {
    const dataList = gridApi.grid.getCheckboxRecords()
    emit('confirm', dataList);
    emit('reload');
    modalApi.close();
}
/** 加载产品下拉框 */
type Option = {
  label: string;
  value: string;
  deviceType?: number | string; // 添加deviceType字段
};
const productOptions = ref<Option[]>([]);
  async function loadProductOptions() {
    const res = await ListProduct({
        page: 1,
        pageSize: 1000,
    });
    if(!res || !res.items){
        productOptions.value = [];
    }else{
        productOptions.value = res.items.map((item: any) => ({
            label: item.productName,
            value: item.productKey,
        }));
    }
    // console.log(productOptions.value);
    gridApi.formApi.updateSchema([
        {
            fieldName: 'productKey',
            component: 'Select',
            label: '所属产品',
            componentProps: {
                placeholder: '请选择产品',
                onUpdateValue: (e: any) => {
                    console.log(e);
                },
                options: productOptions.value,
                showSearch: true,
                filterOption: (input: any, option: any) => {
                    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                },
            },
        },
    ]);
}


/** 表格顶部表单配置 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: detailSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
/** 表格配置 */
const gridOptions: VxeTableGridOptions = {
  checkboxConfig: {
    highlight: true,
  },
  columns: detailColumns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let res = await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
        res.items.forEach((item: any) => {
          item.productName =
            productOptions.value.find((i: any) => item.productKey === i.value)
              ?.label || '';
        });
        // if (gridType.value == '2') {
        //   let newItems = [{ list: res.items }];
        //   res.items = newItems;
        // }
        return res;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: false,
    search: false,
    zoom: false,
  },
};
/** 子设备列表 表格实例 */
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: gridOptions,
  formOptions: formOptions
})


// const
</script>
