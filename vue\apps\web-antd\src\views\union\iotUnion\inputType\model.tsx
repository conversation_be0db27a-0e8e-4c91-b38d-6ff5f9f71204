import type { VbenFormSchema } from "@vben/common-ui";
import type { VxeGridProps } from "@vben/plugins/vxe-table";
import { z } from '#/adapter/form';

/** 表格搜索 */
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'unionKey',
    component: 'Input',
    label: '变量名称',
    componentProps: {
      placeholder: '请输入变量名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  }, {
    fieldName: 'unionKey',
    component: 'Input',
    label: '变量标识',
    componentProps: {
      placeholder: '请输入变量标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
]

/** 表格列配置 */
export const columns: VxeGridProps['columns'] = [
  // 编号
  {
    title: '编号',
    field: 'unionId',
    align: 'center',
    width: 80,
  },
  // 属性名称
  {
    title: '属性名称',
    field: 'unionName',
    align: 'center',
    width: -1,
  },
  //  属性标识
  {
    title: '属性标识',
    field: 'unionKey',
    align: 'center',
    width: -1,
  },
  // 属性单位
  {
    title: '属性单位',
    field: 'remark',
    align: 'center',
    width: -1,
  },
  // 属性值类型
  {
    title: '属性值类型',
    field: 'remark',
    align: 'center',
    width: -1,
  },
  // 属性默认值
  {
    title: '属性默认值',
    field: 'remark',
    align: 'center',
    width: -1,
  },
  // 是否启用
  {
    title: '是否启用',
    field: 'status',
    align: 'center',
    width: 80,
    slots: { default: 'status' },
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];


/** 新增/编辑表单 */
export const editSchema: VbenFormSchema[] = [
  /** 属性ID */
  {
    fieldName: 'unionId',
    component: 'Input',
    label: '属性ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  /** 属性名称 */
  {
    fieldName: 'fieldName',
    component: 'Input',
    label: '属性名称',
    componentProps: {
      placeholder: '请输入属性名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.string()
      .min(1, { message: '请输入属性名称' })
      .refine((value) => value.length<=12, {
        message: '属性名称最长12位',
      })
  },
  /** 属性标识 */
  {
    fieldName: 'modelKey',
    component: 'Input',
    label: '属性标识',
    componentProps: {
      placeholder: '请输入属性标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: z.string().regex(/^[a-zA-Z0-9_]+$/,{ message: '请输入由数字字母下划线组成的属性标识' })
  },
  /** 属性单位 */
  {
    fieldName: 'fieldUnit',
    component: 'Input',
    label: '属性单位',
    componentProps: {
      placeholder: '请输入属性单位',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  /** 属性值类型 */
  {
    fieldName: 'fieldValueType',
    component: 'Select',
    label: '属性值类型',
    componentProps: {
      placeholder: '请选择属性值类型',
      options: [
        {
          label: '数字',
          value: 1
        }, {
          label: '字符串',
          value: 2
        }
      ],
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  /** 属性默认值 */
  {
    fieldName: 'fieldDefaultValue',
    component: 'Input',
    label: '属性默认值',
    componentProps: {
      placeholder: '请输入属性默认值',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  /** 是否历史存储 */
  {
    fieldName: 'isSave',
    component: 'Switch',
    label: '是否历史存储',
    componentProps: {
      style: {
        width: '20px',
      },
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      checkedValue: 1,
      unCheckedValue: 0
    },
    rules: 'selectRequired',
  },
  /** 是否启用 */
  {
    fieldName: 'status',
    component: 'Switch',
    label: '是否启用',
    defaultValue: false,
    componentProps: {
      style: {
        width: '20px',
      },
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      checkedValue: 1,
      unCheckedValue: 0
    },
    rules: 'selectRequired',
  },
];