<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
    </BasicForm>
  </BasicDrawer>
</template>

<script setup lang='ts' name=''>
import { useVbenDrawer, useVbenForm } from '@vben/common-ui';
import { computed, ref } from 'vue'
import { editSchema } from './model';
import { Edit } from '#/api/union/iotUnionField';
import { useRoute } from 'vue-router';
import { z } from '#/adapter/form';
/** 路由实例 */
const route = useRoute();
/** 编辑/新增flag */
const isUpdate = ref(false);

/** 标题 */
const title = computed(() => {
  return isUpdate.value ? "编辑" : "新增";
});

/** 表单实例 */
const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
  handleSubmit: async (data) => {
    console.log(data);
    data.unionKey= route.query.unionId
    const res = await Edit(data);
    console.log(res )
  },
});

/** 抽屉实例 */
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
});


function handleCancel() { }
async function handleConfirm() {
  formApi.submitForm()
}




</script>

<style scoped></style>
