import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { getDictOptions } from '#/utils/dict';
import { DictEnum } from '@vben/constants';
import { renderDict } from '#/utils';

export class State {
  public deviceId = 0; // 设备ID
  public productKey = ''; // 产品标识
  public deviceKey = ''; // 设备标识
  public deviceName = ''; // 设备名称
  public longitude = 0; // 设备坐标（经度）
  public latitude = 0; // 设备坐标（纬度）
  public firmwareVersion = 0; // 固件版本号
  public isShadow = 0; // 是否启用设备影子(0=禁用，1=启用)
  public imgUrl = null; // 图片地址
  public deviceState = 1; // 设备状态（1-未激活，2-禁用，3-在线，4-离线）
  public alarmState = 0; // 报警状态（0-正常 1-场景报警)
  public rssi = 0; // 信号强度（信号极好4格[-55— 0]，信号好3格[-70— -55]，信号一般2格[-85— -70]，信号差1格[-100— -85]）
  public thingsModelValue = null; // 物模型值
  public networkAddress = null; // 设备所在地址
  public networkIp = null; // 设备入网IP
  public status = 0; // 数据状态（"0"正常 "1"停用)
  public tenantId = ''; // 租户ID
  public deptId = 0; // 所属机构
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则
// 表格搜索表单
// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '所属产品',
    componentProps: {
      placeholder: '选择所属产品',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'deviceKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      disabled: false,
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];
// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '设备名称',
    field: 'deviceName',
    align: 'left',
    width: -1,
  },
  {
    title: '设备标识',
    field: 'deviceKey',
    align: 'left',
    width: -1,
  },
  {
    title: '所属产品',
    field: 'productName',
    align: 'left',
    width: -1,
  },
  {
    title: '在线状态',
    field: 'deviceState',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        const stateMap = {
          1: '未激活',
          2: '禁用',
          3: '在线',
          4: '离线'
        };
        return stateMap[row.deviceState] || row.deviceState;
      }
    }
  },
  {
    title: '是否启用',
    field: 'status',
    align: 'center',
    width: 100,
    slots: { default: 'status' },
  },
];

// 表格列接口
export interface RowType {
  deviceId: number;
  productKey: string;
  deviceKey: string;
  deviceName: string;
  longitude: number;
  latitude: number;
  firmwareVersion: number;
  isShadow: number;
  imgUrl: string;
  deviceState: number;
  alarmState: number;
  rssi: number;
  thingsModelValue: string;
  networkAddress: string;
  networkIp: string;
  status: string;
  tenantId: string;
  deptId: number;
  createdDept: number;
  createdBy: number;
  createdAt: string;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
  remark: string;
}

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'deviceId', label: '设备ID' },
  { field: 'productKey', label: '产品标识' },
  { field: 'deviceKey', label: '设备标识' },
  { field: 'deviceName', label: '设备名称' },
  { field: 'longitude', label: '设备坐标（经度）' },
  { field: 'latitude', label: '设备坐标（纬度）' },
  { field: 'firmwareVersion', label: '固件版本号' },
  { field: 'isShadow', label: '是否启用设备影子' },
  { field: 'imgUrl', label: '图片地址' },
  { field: 'deviceState', label: '设备状态' },
  { field: 'alarmState', label: '报警状态' },
  { field: 'rssi', label: '信号强度' },
  { field: 'thingsModelValue', label: '物模型值' },
  { field: 'networkAddress', label: '设备所在地址' },
  { field: 'networkIp', label: '设备入网IP' },
  { field: 'status', label: '是否启用' },
  { field: 'tenantId', label: '租户ID' },
  { field: 'deptId', label: '所属机构' },
  { field: 'createdDept', label: '创建部门' },
  { field: 'createdBy', label: '创建者' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'updatedBy', label: '更新者' },
  { field: 'updatedAt', label: '更新时间' },
  { field: 'deletedBy', label: '删除人' },
  { field: 'deletedAt', label: '删除时间' },
  { field: 'remark', label: '备注' },
];