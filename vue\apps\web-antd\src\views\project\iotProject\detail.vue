<script setup lang="ts">
import { ref, h, onMounted, nextTick } from 'vue';
import { <PERSON>di<PERSON><PERSON>, MdiDelete } from '@vben/icons';
import { Page } from '@vben/common-ui';

import { Card, Switch, Tabs, TabPane, Button, message, Divider, Row, Col, Modal } from 'ant-design-vue';
import { DeviceList, DeviceUnbind, View } from '#/api/project/iotProject';
import { getVxePopupContainer } from '@vben/utils';
import { detailColumns, detailEditSchema, type DetailRowType } from './model';
import { useRoute, useRouter } from 'vue-router';
import { DictEnum } from '@vben/constants';

import { getDictOptions } from '#/utils/dict';
import { useVbenDrawer } from '@vben/common-ui';
import editDrawer from '../iotProject/bindDevice/edit.vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { viewSysDeptApi } from '#/api/system/dept';
import { ListNoPage as ListProduct } from '#/api/device/iotProduct';
import type { VbenFormProps } from '#/adapter/form';
import type {
  VxeTableGridOptions,
  VxeGridListeners,
} from '#/adapter/vxe-table';
import { List, Delete, Status } from '#/api/device/iotDevice';
import type { DeepPartial } from '@vben/types';
import viewDrawer from './view.vue';
import { columns } from '#/views/device/iotDevice/model';
import { router } from '#/router';
import { processImageUrl, handleImageError } from '#/utils/image';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import Relevancy from './relevancy/index.vue';
import IotConsumer from './iotConsumer/index.vue';

const route = useRoute();
const currentTab = ref('a');
const relevancyRef = ref(); // 添加关联产品组件的引用
const iotConsumerRef = ref(); // 添加终端用户组件的引用
const project = ref({
  projectId: route.query.projectId,
  projectName: '',
  longitude: 0,
  latitude: 0,
  region: '',
  regionName: '',
  address: '',
  contactUser: '',
  contactPhone: '',
  status: '',
  tenantId: '',
  deptId: 0,
  createdDept: 0,
  createdBy: 0,
  createdAt: '',
  updatedBy: 0,
  updatedAt: '',
  deletedBy: 0,
  deletedAt: '',
  remark: '',
  deptName: '',
});

async function reloadEditForm() {
  const record = await View({ projectId: route.query.projectId });
  project.value = record;
  // 2. 检查 deptId 是否存在，再查询部门名称
  if (project.value?.deptId) {
    const deptRes = await viewSysDeptApi({ deptId: project.value.deptId });
    project.value.deptName = deptRes.deptName || '未知部门';
    console.log('第二步：查询到部门名称', project.value.deptName);
  } else {
    project.value.deptName = '无部门信息';
    console.log('项目无 deptId，无需查询部门');
  }
}
reloadEditForm()

const deviceRoute = useRoute();
const transportOptions = getDictOptions(DictEnum.TRANSPORT);
// 设备状态颜色映射
const deviceStateColorMap: { [key: string]: { color: string; bg: string; border: string } } = {
  '1': { color: '#faad14', bg: '#fffbe6', border: '1px solid #ffe58f' }, // 未激活（黄色）
  '2': { color: '#52c41a', bg: '#f6ffed', border: '1px solid #b7eb8f' }, // 在线（绿色）
  '3': { color: '#909399', bg: '#f4f6fa', border: '1px solid #d9d9d9' }  // 离线/禁用（灰色）
};
const deviceStateOptions = getDictOptions(DictEnum.DEVICE_STATE); // 设备状态字典

function getDeviceStateLabel(value: string | number) {
  const found = deviceStateOptions.find(opt => String(opt.value) === String(value));
  return found ? found.label : value;
}

type Option = {
  label: string;
  value: string;
  deviceType?: number | string; // 添加deviceType字段
};

const productOptions = ref<Option[]>([]);
async function loadProductOptions() {
  const res = await ListProduct({
    page: 1,
    pageSize: 1000,
  });
  if (!res || !res.items) {
    productOptions.value = [];
  } else {
    productOptions.value = res.items.map((item: any) => ({
      label: item.productName,
      value: item.productKey,
      deviceType: item.deviceType, // 保存deviceType信息
    }));
  }
  console.log('产品选项加载完成，包含deviceType:', productOptions.value);
  gridApi.formApi.updateSchema([
    {
      fieldName: 'productKey',
      component: 'Select',
      label: '所属产品',
      componentProps: {
        placeholder: '请选择产品',
        onUpdateValue: (e: any) => {
          console.log(e);
        },
        options: productOptions.value,
        showSearch: true,
        filterOption: (input: any, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
      },
    },
  ]);
}

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: detailEditSchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<DetailRowType> = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'deviceId',
  },
  columns: detailColumns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        let res = await DeviceList({
          page: page.currentPage,
          pageSize: page.pageSize,
          projectId: project.value.projectId,
          ...formValues,
        });
        res.items.forEach((item: any) => {
          item.productName =
            productOptions.value.find((i: any) => item.productKey === i.value)
              ?.label || '';
        });
        return res;
      },
    },
  },
  toolbarConfig: {
    custom: false,
    export: false,
    refresh: false,
    resizable: true,
    search: true,
    zoom: false,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});
const [ViewDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: viewDrawer,
});
// function handlePreview(record: RowType) {
//   drawerApi.setData({ record });
//   drawerApi.open();
// }
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});
function handleAdd() {
  editDrawerApi.setData({
    id: project.value.projectId,
    update: false,
    view: false,
    productOptions: productOptions.value,
  });
  editDrawerApi.open();
}

async function handleDelete(row: DetailRowType) {
  await DeviceUnbind({
    projectId: project.value.projectId, // 主项目ID
    deviceKeys: row.deviceKey
  });
  message.success('解绑成功');
  await handleRefresh();
}
function handleMultiDelete() {
  const rows = gridApi.grid.getCheckboxRecords();
  const deviceKeys: string[] = [];
  for (const row of rows) {
    deviceKeys.push(row.deviceKey);
  }
  if (deviceKeys.length === 0) {
    message.error('请至少选择一项要删除的数据');
    return;
  }
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${deviceKeys.length}条记录吗？`,
    onOk: async () => {
      await DeviceUnbind({
        projectId: project.value.projectId, // 主项目ID
        deviceKeys: deviceKeys
      });
      message.success('删除成功');
      await handleRefresh();
    },
  });
}
async function handleRefresh() {
  await gridApi.query();
}
async function handleStatusChange(row: DetailRowType) {
  await Status({ deviceId: row.deviceId, status: row.status });
  await message.success('操作成功');
  await handleRefresh();
}
const cardListColumns = [{
  field: 'list',
  slots: {
    default: 'list',
  },
}];

const cardListPager = {
  pageSizes: [12, 24, 36, 48, 60],
  pageSize: 12,
};
const gridType = ref('1');
function handleChangeGrid(value: string) {
  gridType.value = value;
  configGridOptions();
}

function configGridOptions() {
  gridApi.setGridOptions({
    checkboxConfig: {
      highlight: true,
      labelField: 'productId',
    },
    border: gridType.value == '2' ? 'none' : 'default',
    columns: gridType.value == '2' ? cardListColumns : columns,  //columns,
    showHeader: gridType.value == '2' ? false : true,
    pagerConfig: gridType.value == '2' ? cardListPager : {},
  });
  gridApi.reload();
}

const handleDetail = (deviceId: number, tabKey?: string) => {
  // 通过deviceId在表格数据中查找对应的设备信息
  const deviceData = gridApi.grid.getData();
  const currentDevice = deviceData.find((item: any) => item.deviceId === deviceId);

  if (currentDevice) {
    // 通过productKey查找对应的产品信息
    const productInfo = productOptions.value.find((p: any) => p.value === currentDevice.productKey);

    // 判断是否为监控设备（deviceType = 4）
    const isMonitorDevice = productInfo && (
      productInfo.deviceType === 4 ||
      productInfo.deviceType === '4'
    );

    console.log('设备详情跳转判断:', {
      deviceId,
      productKey: currentDevice.productKey,
      productInfo,
      isMonitorDevice
    });

    let url = '';
    if (isMonitorDevice) {
      // 监控设备跳转到监控详情页，传递deviceKey参数
      url = `/device/monitorDetail?deviceKey=${currentDevice.deviceKey}`;
    } else {
      // 普通设备跳转到普通详情页
      url = `/device/deviceDetail?deviceId=${deviceId}`;
    }

    if (tabKey) {
      url += `&tab=${tabKey}`;
    }

    router.push(url);
  } else {
    console.error('未找到设备信息，使用默认详情页');
    let url = `/device/deviceDetail?deviceId=${deviceId}`;
    if (tabKey) {
      url += `&tab=${tabKey}`;
    }
    router.push(url);
  }
};

// 刷新设备列表数据
async function refreshDeviceList() {
  try {
    console.log('刷新设备列表');
    if (gridApi && gridApi.query) {
      await gridApi.query();
      console.log('设备列表刷新完成');
    } else {
      console.error('gridApi不存在或没有query方法');
    }
  } catch (error) {
    console.error('刷新设备列表失败:', error);
  }
}

// 刷新终端用户数据
async function refreshConsumerList() {
  try {
    console.log('刷新终端用户列表');

    if (iotConsumerRef.value) {
      console.log('iotConsumerRef.value存在');

      // 检查IotConsumer组件是否有刷新方法
      if (iotConsumerRef.value.handleRefresh) {
        console.log('调用IotConsumer的handleRefresh方法');
        await iotConsumerRef.value.handleRefresh();
      } else if (iotConsumerRef.value.refresh) {
        console.log('调用IotConsumer的refresh方法');
        await iotConsumerRef.value.refresh();
      } else {
        console.log('IotConsumer组件没有暴露刷新方法，尝试其他方式');
        // 如果组件没有暴露刷新方法，可以尝试其他方式
      }

      console.log('终端用户列表刷新完成');
    } else {
      console.error('iotConsumerRef.value不存在');
    }
  } catch (error) {
    console.error('刷新终端用户列表失败:', error);
  }
}

// 刷新关联产品数据
async function refreshRelevancy() {
  try {
    console.log('refreshRelevancy被调用');
    console.log('relevancyRef.value:', relevancyRef.value);

    if (relevancyRef.value) {
      console.log('relevancyRef.value存在');
      console.log('handleRefresh方法:', relevancyRef.value.handleRefresh);

      if (relevancyRef.value.handleRefresh) {
        console.log('开始调用handleRefresh方法');
        await relevancyRef.value.handleRefresh();
        console.log('handleRefresh方法调用完成');
      } else {
        console.error('handleRefresh方法不存在');
      }
    } else {
      console.error('relevancyRef.value不存在');
    }
  } catch (error) {
    console.error('刷新关联产品表格失败:', error);
  }
}

// 监听tab切换
function handleTabChange(activeKey: string | number) {
  const key = String(activeKey);
  console.log('Tab切换到:', key);
  currentTab.value = key;

  // 根据不同的tab执行对应的刷新操作
  nextTick(() => {
    setTimeout(() => {
      switch (key) {
        case 'a':
          // 切换到绑定设备tab时刷新设备列表
          console.log('刷新绑定设备列表');
          refreshDeviceList();
          break;
        case 'b':
          // 切换到终端用户tab时刷新用户列表
          console.log('刷新终端用户列表');
          refreshConsumerList();
          break;
        case 'c':
          // 切换到关联产品tab时刷新产品列表
          console.log('刷新关联产品列表');
          refreshRelevancy();
          break;
        default:
          console.log('未知的tab:', key);
      }
    }, 200); // 延迟确保组件完全渲染
  });
}

onMounted(() => {
  loadProductOptions();
  // 1. 获取路由参数
  const { productName, productKey } = deviceRoute.query;
  if (productKey) {
    // 2. 设置表单字段
    gridApi.formApi.setFieldValue('productKey', productKey); // 你的下拉框字段名是 productKey
    // 3. 自动触发一次搜索
    console.log('自动触发搜索', productName);
    gridApi.query();
  }
});

</script>

<template>
  <Page class="h-full overflow-y-auto">
    <Card class="mb-4">
      <label class="ml-2 ">项目: {{ project.projectName }}</label>
      <label class="ml-4">责任人: {{ project.contactUser }}</label>
      <label class="ml-4">{{ project.contactPhone }}</label>
      <label class="ml-4">所属机构:{{ project.deptName }} </label>
    </Card>
    <Card style="height:calc(100vh - 200px);" class="overflow-y-auto">
      <Tabs v-model:activeKey="currentTab" @change="handleTabChange">
        <TabPane key="a" tab="绑定设备">
          <div class="p-0 m-0 h-[800px]">
            <Grid table-title="设备列表">
              <template #toolbar-tools>
                <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiPlus)" @click="handleAdd"
                  v-access:code="'cpm:device:iotDevice:edit'">
                  新增
                </Button>
                <Button class="mr-2 flex items-center" type="primary" :disabled="!CheckboxChecked" :icon="h(MdiDelete)"
                  @click="handleMultiDelete" v-access:code="'cpm:device:iotDevice:delete'">
                  删除
                </Button>
                <RadioGroup class="mr-2 flex items-center" @change="handleChangeGrid($event.target.value)"
                  v-model:value="gridType">
                  <RadioButton value="1"
                    style="display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0;">
                    <IconifyIcon icon="ant-design:menu-outlined" />
                  </RadioButton>
                  <RadioButton value="2"
                    style="display: flex; align-items: center; justify-content: center; width: 32px; height: 32px; padding: 0;">
                    <IconifyIcon icon="mdi:view-grid" />
                  </RadioButton>
                </RadioGroup>
              </template>
              <template #status="{ row }">
                <Switch v-model:checked="row.status" :checkedValue="'0'" :unCheckedValue="'1'"
                  @change="handleStatusChange(row)" :disabled="!hasAccessByCodes(['cpm:device:iotDevice:status'])" />
              </template>
              <template #action="{ row }">
                <div class="flex items-center">
                  <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleDetail(row.deviceId)"
                    v-access:code="'cpm:device:iotDevice:view'">
                    详情
                  </Button>
                  <AccessControl :codes="['cpm:device:iotDevice:delete']" type="code">
                    <Popconfirm title="确定解除绑定吗？" :get-popup-container="getVxePopupContainer" placement="left"
                      @confirm="handleDelete(row)">
                      <Button class="mr-2 border-none p-0" :block="false" type="link" danger @click="handleDelete(row)">
                        解除绑定
                      </Button>
                    </Popconfirm>
                  </AccessControl>
                </div>
              </template>
              <template #deviceState="{ row }">
                <Tag :style="{
                  borderRadius: '4px',
                  minWidth: '48px',
                  textAlign: 'center',
                  color: row.deviceState === 2 ? '#52c41a' : row.deviceState === 1 ? '#faad14' : '#909399',
                  background: row.deviceState === 2 ? '#f6ffed' : row.deviceState === 1 ? '#fffbe6' : '#f4f6fa',
                  border: row.deviceState === 2
                    ? '1px solid #b7eb8f'
                    : row.deviceState === 1
                      ? '1px solid #ffe58f'
                      : '1px solid #d9d9d9'
                }">
                  {{ getDeviceStateLabel(row.deviceState) }}
                </Tag>
              </template>
            </Grid>
          </div>
          <EditDrawer @reload="handleRefresh" />
          <ViewDrawer />
        </TabPane>
        <TabPane key="b" tab="终端用户">
          <IotConsumer ref="iotConsumerRef" :project="project" class="p-0 m-0" />
        </TabPane>
        <TabPane key="c" tab="关联产品">
          <Relevancy ref="relevancyRef" :project="project" class="p-0 m-0" />
        </TabPane>
      </Tabs>
    </Card>
  </Page>
</template>
