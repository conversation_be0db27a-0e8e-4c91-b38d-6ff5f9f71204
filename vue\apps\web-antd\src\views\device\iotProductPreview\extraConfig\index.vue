<script lang="ts" setup>
import { h, ref, onMounted, watch, nextTick } from 'vue';
import { Button, message, Card, Switch } from 'ant-design-vue';
import { Page, useVbenForm, useVbenModal } from '@vben/common-ui';
import { View, ExtraConfigEdit } from '#/api/device/iotProduct';
import gpsFieldSelector from './gpsFieldSelector.vue';
import { List } from '#/api/device/iotProductModel';
import { getDictOptions } from '#/utils/dict';
import { DictEnum } from '@vben/constants';
const emit = defineEmits(['reload']);
const props = defineProps({
  productKey: {
    type: String,
    required: true,
  },
  published: {
    type: Boolean,
    required: true,
  },
});

// 各个配置模块的开关状态
const gpsEnabled = ref(true);
const simCardEnabled = ref(true);
const tsdbEnabled = ref(true);

type ExtraConfig = {
  latAndLongKey: {
    modelType: string;
    modelKey: string;
    outputKey: string;
  },
  iccidKey: {
    modelType: string;
    modelKey: string;
    outputKey: string;
  },
  imsiKey: {
    modelType?: string;
    modelKey?: string;
    outputKey?: string;
  },
  storePolicy: string;
}

const data = ref<ExtraConfig>({
  latAndLongKey: {
    modelType: 'property',
    modelKey: '',
    outputKey: ''
  },
  iccidKey: {
    modelType: 'property',
    modelKey: '',
    outputKey: ''
  },
  imsiKey: {
    modelType: 'property',
    modelKey: '',
    outputKey: ''
  },
  storePolicy: '1',
});

const modelPropertyOptions = ref<any[]>([]);

// gps 弹窗相关状态
const [gpsModal, gpsModalApi] = useVbenModal({
  title: '选择物模型字段',
  connectedComponent: gpsFieldSelector,
});

// 通用字段选择函数
function selectField(fieldKey: string) {
  try {
    // 根据字段key获取对应的初始值和标题
    let initialValue: any;
    let title: string;

    switch (fieldKey) {
      case 'latAndLongKey':
        initialValue = data.value.latAndLongKey;
        title = '选择经纬度物模型字段';
        break;
      case 'iccidKey':
        initialValue = data.value.iccidKey;
        title = '选择ICCID物模型字段';
        break;
      case 'imsiKey':
        initialValue = data.value.imsiKey;
        title = '选择IMSI物模型字段';
        break;
      default:
        initialValue = {};
        title = '选择物模型字段';
        break;
    }

    const modalData = {
      key: fieldKey,
      productKey: props.productKey,
      initialValue: initialValue,
      title: title,
    }

    console.log(`准备打开${fieldKey}字段选择弹窗...`, modalData);
    gpsModalApi.setData(modalData);
    gpsModalApi.open();
  } catch (error) {
    console.error('打开弹窗时出错:', error);
  }
}

// 为了保持向后兼容，保留原函数名
function selectGpsField() {
  if (!gpsEnabled.value) {
    message.warning('请先开启GPS配置');
    return;
  }
  selectField('latAndLongKey');
}

// 新增具体字段选择函数
function selectIccidField() {
  if (!simCardEnabled.value) {
    message.warning('请先开启物联卡配置');
    return;
  }
  selectField('iccidKey');
}

function selectImsiField() {
  if (!simCardEnabled.value) {
    message.warning('请先开启物联卡配置');
    return;
  }
  selectField('imsiKey');
}

// 存储策略变化处理
async function handleStorePolicyChange(value: string) {
  if (!tsdbEnabled.value) {
    message.warning('请先开启TSDB配置');
    return;
  }

  try {
    data.value.storePolicy = value;

    await ExtraConfigEdit({
      productKey: props.productKey,
      extraConfig: data.value,
    });

    message.success('存储策略配置保存成功');
  } catch (error) {
    console.error('保存存储策略失败:', error);
    message.error('存储策略配置保存失败，请重试');
  }
}


function updateSchema() {
  form1Api.updateSchema(
    [
      {
        fieldName: 'latAndLongKey',
        componentProps: {
          disabled: props.published || !gpsEnabled.value,
        },
      },
    ]
  );
  form2Api.updateSchema(
    [
      {
        fieldName: 'iccidKey',
        componentProps: {
          disabled: props.published || !simCardEnabled.value,
        },
      },
      {
        fieldName: 'imsiKey',
        componentProps: {
          disabled: props.published || !simCardEnabled.value,
        },
      },
    ]
  );
  form3Api.updateSchema(
    [
      {
        fieldName: 'storePolicy',
        componentProps: {
          disabled: props.published || !tsdbEnabled.value,
        },
      },
    ]
  );
}

function getModelStr(model: any) {
  let res = '';
  if (model && model.modelType) {
    res = model.modelType;

    if (model.modelKey) {
      res = model.modelType + "." + model.modelKey;

      if (model.outputKey) {
        res = model.modelType + "." + model.modelKey + "." + model.outputKey;
      }
    }

  }
  return res;
}

// 判断GPS配置是否有内容
function hasGpsContent(): boolean {
  return !!(data.value.latAndLongKey &&
    data.value.latAndLongKey.modelKey &&
    data.value.latAndLongKey.modelKey.trim() !== '');
}

// 判断物联卡配置是否有内容
function hasSimCardContent(): boolean {
  return !!(data.value.iccidKey &&
    data.value.iccidKey.modelKey &&
    data.value.iccidKey.modelKey.trim() !== '') ||
    !!(data.value.imsiKey &&
      data.value.imsiKey.modelKey &&
      data.value.imsiKey.modelKey.trim() !== '');
}

// 判断TSDB配置是否有内容
function hasTsdbContent(): boolean {
  return !!(data.value.storePolicy &&
    typeof data.value.storePolicy === 'string' &&
    data.value.storePolicy.trim() !== '');
}

async function handleRefresh() {
  const res = await View({ productKey: props.productKey });
  const extraConfig = res?.extraConfig || {};

  // 确保 storePolicy 是字符串类型
  if (extraConfig.storePolicy !== undefined && extraConfig.storePolicy !== null) {
    extraConfig.storePolicy = String(extraConfig.storePolicy);
  } else {
    extraConfig.storePolicy = '1'; // 默认值
  }

  data.value = extraConfig;
  console.log('从后端获取的配置数据:', data.value);

  // 开关状态逻辑：如果产品已发布，则开关关闭且禁用；如果未发布，则可以使用
  if (props.published) {
    gpsEnabled.value = false;
    simCardEnabled.value = false;
    tsdbEnabled.value = false;
  } else {
    // 未发布状态下，检查是否有配置内容来决定开关状态
    gpsEnabled.value = hasGpsContent();
    simCardEnabled.value = hasSimCardContent();
    tsdbEnabled.value = hasTsdbContent();
  }

  const modelList = await List({ productKey: props.productKey, type: 'property', page: 1, pageSize: 1000 });
  console.log("modelList", modelList);
  modelPropertyOptions.value = modelList.items?.map((item: any) => ({
    label: item.modelName,
    value: item.modelKey,
  })) || [];
  // 准备表单值
  const latAndLongValue = getModelStr(data.value.latAndLongKey);
  const iccidValue = getModelStr(data.value.iccidKey);
  const imsiValue = getModelStr(data.value.imsiKey);
  const storePolicyValue = typeof data.value.storePolicy === 'string' ? data.value.storePolicy : '';

  console.log('准备设置的表单值:', {
    latAndLongValue,
    iccidValue,
    imsiValue,
    storePolicyValue,
    rawData: data.value
  });

  // 使用 nextTick 确保表单已经渲染完成
  await nextTick();

  // 添加短暂延迟确保表单API完全准备好
  await new Promise(resolve => setTimeout(resolve, 100));

  // 逐个设置字段值
  try {
    if (latAndLongValue) {
      await form1Api.setFieldValue('latAndLongKey', latAndLongValue);
      console.log('GPS字段设置成功:', latAndLongValue);
    }

    if (iccidValue) {
      await form2Api.setFieldValue('iccidKey', iccidValue);
      console.log('ICCID字段设置成功:', iccidValue);
    }

    if (imsiValue) {
      await form2Api.setFieldValue('imsiKey', imsiValue);
      console.log('IMSI字段设置成功:', imsiValue);
    }

    if (storePolicyValue) {
      await form3Api.setFieldValue('storePolicy', storePolicyValue);
      console.log('存储策略字段设置成功:', storePolicyValue);
    }

    // 验证设置结果
    const currentForm1Values = await form1Api.getValues();
    const currentForm2Values = await form2Api.getValues();
    const currentForm3Values = await form3Api.getValues();
    console.log('设置后的表单值:', { currentForm1Values, currentForm2Values, currentForm3Values });

  } catch (error) {
    console.error('设置表单值时出错:', error);
  }
  updateSchema();
}

onMounted(async () => {
  console.log('gpsModalApi:', gpsModalApi);
  await handleRefresh();
});

watch(
  () => [props.published, props.productKey],
  async () => {
    await handleRefresh();
  },
);



const [Form1, form1Api] = useVbenForm({
  commonConfig: {
    labelWidth: 150,
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'horizontal',
  schema: [
    {
      fieldName: 'latAndLongKey',
      label: '经纬度对应物模型字段',
      component: 'Input',
      defaultValue: '',
      componentProps: {
        allowClear: true,
        readonly: true,
        placeholder: '请选择经纬度对应的物模型字段',
        addonAfter: h(Button, {
          type: 'link',
          onClick: () => selectGpsField()
        }, '选择')
      },
    }
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

const [Form2, form2Api] = useVbenForm({
  commonConfig: {
    labelWidth: 150,
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'horizontal',
  schema: [
    {
      fieldName: 'iccidKey',
      label: 'ICCID对应物模型字段',
      component: 'Input',
      defaultValue: '',
      componentProps: {
        allowClear: true,
        readonly: true,
        placeholder: '请选择ICCID对应物模型字段',
        addonAfter: h(Button, {
          type: 'link',
          onClick: () => selectIccidField()
        }, '选择')
      },
    },
    {
      fieldName: 'imsiKey',
      label: 'IMSI对应物模型字段',
      component: 'Input',
      defaultValue: '',
      componentProps: {
        options: modelPropertyOptions.value,
        allowClear: true,
        readonly: true,
        placeholder: '请选择IMSI对应的物模型字段',
        addonAfter: h(Button, {
          type: 'link',
          onClick: () => selectImsiField()
        }, '选择')
      },
    },

  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});
const [Form3, form3Api] = useVbenForm({
  commonConfig: {
    labelWidth: 100,
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'horizontal',
  schema: [
    {
      fieldName: 'storePolicy',
      label: '存储策略',
      component: 'Select',
      defaultValue: '',
      componentProps: {
        options: getDictOptions(DictEnum.STORE_POLICY),
        onChange: handleStorePolicyChange,
      },
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});

async function handleFieldConfirm(field: any) {
  console.log('字段选择确认:', field);
  if (field && field.key) {
    // 获取字段的显示名称
    const fieldNames: Record<string, string> = {
      'latAndLongKey': '经纬度',
      'iccidKey': 'ICCID',
      'imsiKey': 'IMSI'
    };

    const fieldDisplayName = fieldNames[field.key] || field.key;

    try {
      // 更新数据
      switch (field.key) {
        case 'latAndLongKey':
          data.value.latAndLongKey = field.formData;
          form1Api.setFieldValue('latAndLongKey', getModelStr(field.formData));
          break;
        case 'iccidKey':
          data.value.iccidKey = field.formData;
          form2Api.setFieldValue('iccidKey', getModelStr(field.formData));
          break;
        case 'imsiKey':
          data.value.imsiKey = field.formData;
          form2Api.setFieldValue('imsiKey', getModelStr(field.formData));
          break;
        default:
          message.warning(`未知字段类型: ${field.key}`);
          return;
      }

      // 获取存储策略的当前值
      const form3Values = await form3Api.getValues();
      if (form3Values.storePolicy) {
        data.value.storePolicy = form3Values.storePolicy;
      }

      // 直接调用保存接口
      await ExtraConfigEdit({
        productKey: props.productKey,
        extraConfig: data.value,
      });

      message.success(`${fieldDisplayName}字段配置保存成功`);
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error(`${fieldDisplayName}字段配置保存失败，请重试`);
    }
  } else {
    message.error('请选择有效的字段');
  }
}

// GPS开关状态变化处理
function handleGpsToggle(checked: any) {
  const isEnabled = Boolean(checked);
  gpsEnabled.value = isEnabled;

  if (!isEnabled) {
    // 当关闭GPS时，清空相关字段
    data.value.latAndLongKey = {
      modelType: 'property',
      modelKey: '',
      outputKey: ''
    };
    form1Api.setFieldValue('latAndLongKey', '');
  }
  updateSchema();
}

// 物联卡开关状态变化处理
function handleSimCardToggle(checked: any) {
  const isEnabled = Boolean(checked);
  simCardEnabled.value = isEnabled;

  if (!isEnabled) {
    // 当关闭物联卡配置时，清空相关字段
    data.value.iccidKey = {
      modelType: 'property',
      modelKey: '',
      outputKey: ''
    };
    data.value.imsiKey = {
      modelType: 'property',
      modelKey: '',
      outputKey: ''
    };
    form2Api.setFieldValue('iccidKey', '');
    form2Api.setFieldValue('imsiKey', '');
  }
  updateSchema();
}

// TSDB开关状态变化处理
function handleTsdbToggle(checked: any) {
  const isEnabled = Boolean(checked);
  tsdbEnabled.value = isEnabled;

  if (!isEnabled) {
    // 当关闭TSDB配置时，清空相关字段
    data.value.storePolicy = '';
    form3Api.setFieldValue('storePolicy', '');
  }
  updateSchema();
}
</script>
<template>
  <Page auto-content-height>

    <div class="flex justify-left gap-4">
      <Card key="gpsConfig" class="mb-4 gap-4 w-1/3">
        <template #title>
          <div class="flex items-center justify-between w-full">
            <span>GPS配置</span>
            <Switch v-model:checked="gpsEnabled" @change="handleGpsToggle" :disabled="props.published" size="small" />
          </div>
        </template>
        <Form1 />
      </Card>
      <Card key="simCardConfig" class="mb-4 gap-4 w-1/3">
        <template #title>
          <div class="flex items-center justify-between w-full">
            <span>物联卡配置</span>
            <Switch v-model:checked="simCardEnabled" @change="handleSimCardToggle" :disabled="props.published"
              size="small" />
          </div>
        </template>
        <Form2 />
      </Card>
      <Card key="tsdbConfig" class="mb-4 gap-4 w-1/3">
        <template #title>
          <div class="flex items-center justify-between w-full">
            <span>TSDB配置</span>
            <Switch v-model:checked="tsdbEnabled" @change="handleTsdbToggle" :disabled="props.published" size="small" />
          </div>
        </template>
        <Form3 />
      </Card>
    </div>
    <!-- GPS字段选择弹窗 -->
    <gpsModal @confirm="handleFieldConfirm" />
  </Page>
</template>
