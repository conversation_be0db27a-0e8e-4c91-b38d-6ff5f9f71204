import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';

import { Switch } from '#/api/project/iotConsumer';
import type { DescItem } from '#/components/description';
import { renderDict } from '#/utils';
import { DictEnum } from '@vben/constants';


// export class State {
// 	public consumerId = 0; // 终端用户ID
// 	public userName = ''; // 用户名
// 	public userPassword = ''; // 登录密码
// 	public userSalt = ''; // 加密盐
// 	public nickName = ''; // 用户昵称
// 	public realName = null; // 真实姓名
// 	public sex = null; // 性别（0男 1女 2未知）
// 	public phone = ''; // 手机
// 	public email = null; // 邮箱
// 	public status = 0; // 状态：0=正常，1=停用
// 	public projectId = 0; // 项目ID
// 	public tenantId = ''; // 租户ID
// 	public deptId = 0; // 所属机构
// 	public createdDept = 0; // 创建部门
// 	public createdBy = 0; // 创建者
// 	public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
// 	public createdAt = ''; // 创建时间
// 	public updatedBy = 0; // 更新者
// 	public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
// 	public updatedAt = ''; // 更新时间
// 	public deletedBy = 0; // 删除人
// 	public deletedBySumma?: null | MemberSumma = null; // 删除人摘要信息
// 	public deletedAt = ''; // 删除时间
// 	public remark = null; // 备注

// 	constructor(state?: Partial<State>) {
// 		if (state) {
// 			Object.assign(this, state);
// 		}
// 	}
// }

// export function newState(state: State | Record<string, any> | null): State {
// 	if (state !== null) {
// 		if (state instanceof State) {
// 			return cloneDeep(state);
// 		}
// 		return new State(state);
// 	}
// 	return new State();
// }

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
	{
		fieldName: 'productKey',
		component: 'Select',
		label: '所属产品',
	},
	{
		fieldName: 'deviceKey',
		component: 'Input',
		label: '设备标识',
		componentProps: {
			disabled: false,
			placeholder: '请输入设备标识',
			onUpdateValue: (e: any) => {
				console.log(e);
			},
		},
	},
];

// 表格列
export const columns: VxeGridProps['columns'] = [
	{
		title: '编号',
		field: 'deviceConsumerId',
		align: 'center',
		width: 60,
	},
	{
		title: '设备名称',
		field: 'device.deviceName',
		align: 'center',
		width: -1,
	},
	{
		title: '设备别名',
		field: 'aliasName',
		align: 'center',
		width: -1,
	},
	{
		title: '设备标识',
		field: 'device.deviceKey',
		align: 'center',
		width: -1,
	},
	{
		title: '所属产品',
		field: 'device.productName',
		align: 'center',
		width: -1,
	},
	{
		title: '版本',
		field: 'device.firmwareVersion',
		align: 'center',
		width: 100,
	},
	{
		title: '分享状态',
		field: 'shareStatus',
		align: 'center',
		width: 100,
		slots: {
			default: 'shareStatusSlot' // 插槽名称，对应模板中的 #shareStatusSlot
		}
	},
	{
		title: '激活时间',
		field: 'device.activeTime',
		align: 'center',
		width: -1,
	},
	{
		title: '在线状态',
		field: 'device.deviceState',
		align: 'center',
		width: 100,
		slots: { default: 'deviceState' },
	},
	{ title: '操作', width: 150, slots: { default: 'action' } },
];

export interface RowType {
	deviceId: number;
	productKey: string;
	deviceKey: string;
	deviceName: string;
	longitude: number;
	latitude: number;
	firmwareVersion: number;
	isShadow: number;
	imgUrl: string;
	deviceState: number;
	alarmState: number;
	rssi: number;
	thingsModelValue: string;
	networkAddress: string;
	networkIp: string;
	status: string;
	tenantId: string;
	deptId: number;
	createdDept: number;
	createdBy: number;
	createdAt: string;
	updatedBy: number;
	updatedAt: string;
	deletedBy: number;
	deletedAt: string;
	remark: string;
}

// 分享表格列
export const shareInfoColumns: VxeGridProps['columns'] = [
	{
		title: '编号',
		field: 'deviceConsumerId',
		align: 'center',
		width: 60,
	},
	{
		title: '用户名',
		field: 'deviceName',
		align: 'left',
		width: -1,
	},
	{
		title: '手机',
		field: 'deviceKey',
		align: 'left',
		width: -1,
	},
	{
		title: '分享时间',
		field: 'productKey',
		align: 'left',
		width: -1,
	},
	{
		title: '状态',
		field: 'deviceState',
		align: 'center',
		width: 100,
		// slots: { default: 'deviceState' },
	},
];


