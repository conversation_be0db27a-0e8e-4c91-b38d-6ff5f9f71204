<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <BasicForm>
      <template #computed="{ row }">
        <Space direction="vertical">
        <RadioGroup>
          <Radio>1</Radio>
          <Radio>2</Radio>
        </RadioGroup>
        <Space>
          <label>每</label>
          <Select></Select>
          <label>运算一次</label>
        </Space></Space>
      </template>

    </BasicForm>
  </BasicDrawer>
</template>

<script setup lang='ts' name=''>
import { Radio, RadioGroup, Space, Select } from 'ant-design-vue';
import { useVbenDrawer, useVbenForm } from '@vben/common-ui';
import { computed, ref } from 'vue'
import { editSchema } from './model';
/** 编辑/新增flag */
const isUpdate = ref(false);

/** 标题 */
const title = computed(() => {
  return isUpdate.value ? "编辑" : "新增";
});
/** 表单实例 */
const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-3',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3 gap-x-4',
});
/** 抽屉实例 */
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
});


function handleCancel() { }
function handleConfirm() { }
</script>

<style scoped></style>