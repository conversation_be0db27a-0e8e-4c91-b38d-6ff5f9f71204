import { requestClient } from '#/api/request';

// 获取终端用户绑定设备表列表
export function List(params:any) {
  return requestClient.get<any>('project/iotDeviceConsumer/list', { params });
}

// 删除/批量删除终端用户绑定设备表
export function Delete(params:any) {
  return requestClient.post<any>('project/iotDeviceConsumer/delete', { ...params });
}

// 添加/编辑终端用户绑定设备表
export function Edit(params:any) {
  return requestClient.post<any>('project/iotDeviceConsumer/edit', { ...params });
}

// 获取终端用户绑定设备表指定详情
export function View(params:any) {
  return requestClient.get<any>('project/iotDeviceConsumer/view', { params });
}

// 导出终端用户绑定设备表
export function Export(params:any) {
  return requestClient.post<Blob>('/project/iotDeviceConsumer/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}