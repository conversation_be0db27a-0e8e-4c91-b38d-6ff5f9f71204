<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import { Page } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { Card, Tabs, TabPane, Button, message, Divider, } from 'ant-design-vue';
import { Detail } from '#/api/device/iotDevice';
import { detailEditSchema } from './model';
import IotProductModel from '../iotProductModel/index.vue';
import { useRoute, useRouter } from 'vue-router';
import { useTabbarStore } from '@vben/stores';
import { ExportModel, ListNoPage as ListProduct } from '#/api/device/iotProduct';
import { downloadByData } from '#/utils/file/download';
import IotDevicebindConsumer from './alarmUser/iotDevicebindConsumer.vue';
import PropertiesTemplate from './properties.vue';
import IotDeviceLog from '../iotDeviceLog/index.vue';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import IotDeviceRelation from '../iotDeviceRelation/index.vue';
import Alarmlog from './alarmlog/index.vue';
import Feiqirizhi from './feiqirizhi/index.vue';
import Baojingyonghu from './baojingyonghu/index.vue';
import IotPollTask from '../iotPollTask/tab.vue';
import DeviceFunction from './iotDeviceFunction/deviceFunction.vue';
import EditAlarmUser from './baojingyonghu/edit.vue';
import VideoMonitoring from './videoMonitoring/index.vue';
import { useVbenDrawer } from '@vben/common-ui';

const route = useRoute();
const router = useRouter();
const tabbarStore = useTabbarStore();
const iotProductModel = ref(IotProductModel)
const iotProductModelFuntion = ref(IotProductModel)
const iotProductModelEvent = ref(IotProductModel)
const iotProductModelTag = ref(IotProductModel)
const baojingyonghuRef = ref()
const iotDevicebindConsumerRef = ref()
const isUpdate = ref(false);

const deviceEnabled = ref(false);
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: EditAlarmUser,
  // 可配置抽屉宽度、标题等，比如：

});
const device = ref({
  deviceId: route.query.deviceId,
  productKey: '',
  deviceKey: '',
  deviceName: '',
  longitude: 0,
  latitude: 0,
  firmwareVersion: 0,
  isShadow: 0,
  imgUrl: '',
  deviceState: 0,
  alarmState: 0,
  rssi: 0,
  thingsModelValue: '',
  networkAddress: '',
  networkIp: '',
  status: '',
  tenantId: '',
  deptId: 0,
  createdDept: 0,
  createdBy: 0,
  remark: '',
  gatewayKey: '',  // 网关标识
  gatewayName: '', // 网关名称，用于显示
  gatewayId: 0     // 网关ID，用于存储
});

const product = ref({
  productId: 0,
  productKey: '',
  productName: '',
  categoryId: 0,
  categoryName: '',
  deviceType: '',
  firmwareType: '',
  networkType: '',
  isPrivate: 0,
  transport: 0,
  channelType: 0,
  vertificateMethod: 0,
  thingsModelsJson: '',
  locationWay: 0,
  imgUrl: '',
  publishStatus: 0,
});

const tsl = ref({
  key: '',
  name: '',
  properties: [],
  functions: [],
  events: [],
  tags: [],
  extConfig: [],
});

const properties = ref(Map<string, any>);

const currentTab = ref('a');

const currentModelType = ref('property');
const refreshKey = ref(0);
async function refresh() {
  isUpdate.value = false;
  console.log('handleRefresh')
  await reloadEditForm()
  deviceEnabled.value = String(device.value.status) === '0';
  await loadProductOptions();
  if (currentTab.value == 'b') {
    refreshIotProductModel(currentModelType.value);
  }
  // 每次刷新时+1，强制重建子组件
  refreshKey.value++;

  // 刷新报警用户表格 - 无论当前在哪个Tab，都尝试刷新
  await refreshBaojingyonghu();
}

// 专门的刷新报警用户表格方法
async function refreshBaojingyonghu() {
  try {
    if (baojingyonghuRef.value && baojingyonghuRef.value.handleRefresh) {
      console.log('刷新报警用户表格');
      await baojingyonghuRef.value.handleRefresh();
    }
  } catch (error) {
    console.error('刷新报警用户表格失败:', error);
  }
}

// 专门的刷新设备绑定终端用户表格方法
async function refreshIotDevicebindConsumer() {
  try {
    if (iotDevicebindConsumerRef.value && iotDevicebindConsumerRef.value.handleRefresh) {
      console.log('刷新设备绑定终端用户表格');
      await iotDevicebindConsumerRef.value.handleRefresh();
    }
  } catch (error) {
    console.error('刷新设备绑定终端用户表格失败:', error);
  }
}

// 处理报警用户tab切换的函数
function onAlarmUserTabChange(key: string) {
  console.log('报警用户tab切换:', key);
  currentModelType.value = key;

  if (key === 'function') {
    // 切换到"设备绑定终端用户"tab时刷新数据
    nextTick(() => {
      refreshIotDevicebindConsumer();
    });
  } else if (key === 'property') {
    // 切换到"设备报警用户"tab时刷新数据
    nextTick(() => {
      refreshBaojingyonghu();
    });
  }
}
const tabRoute = useRoute();

onMounted(async () => {
  if (tabRoute.query.tab) {
    currentTab.value = tabRoute.query.tab as string;
  }
  // 如果初始就是运行状态tab，立即刷新
  if (currentTab.value === 'a1') {
    refresh();
  }
  refresh();
});

watch(currentTab, (val) => {
  if (val === 'a1') {
    refresh();
  }
});

// 监听路由变化，当deviceId改变时重新加载数据
watch(() => route.query.deviceId, async (newDeviceId, oldDeviceId) => {
  console.log('🔍 路由监听器触发:', { newDeviceId, oldDeviceId });
  if (newDeviceId && newDeviceId !== oldDeviceId) {
    console.log('✅ 设备ID发生变化，开始重新加载数据:', { oldDeviceId, newDeviceId });

    // 重置当前标签页到第一个
    currentTab.value = 'a';

    // 重新加载设备详情数据
    await reloadEditForm();

    // 设置设备启用状态
    if (device.value.status == '0') {
      deviceEnabled.value = true;
    } else {
      deviceEnabled.value = false;
    }

    // 如果当前是运行状态tab，重新刷新
    if (currentTab.value === 'a1') {
      refresh();
    }

    console.log('✅ 数据重新加载完成');
  } else {
    console.log('⚠️ 设备ID未变化或无效，跳过重新加载');
  }
}, { immediate: false });
type Option = {
  label: string;
  value: string;
}
const productOptions = ref<Option[]>([]);

async function loadProductOptions() {
  const res = await ListProduct({
    page: 1,
    pageSize: 1000,
  });
  productOptions.value = res.items.map((item: any) => ({
    label: item.productName,
    value: item.productKey,
  }));
  console.log(productOptions.value);
  formApi.updateSchema([
    {
      fieldName: 'productKey',
      component: 'Select',
      label: '所属产品',
      componentProps: {
        placeholder: '请选择状态',
        onUpdateValue: (e: any) => {
          console.log(e);
        },
        options: productOptions.value,
      },
    }]);
}

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-2',
  },
  layout: 'horizontal',
  schema: detailEditSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-6 gap-x-4',
});

async function reloadEditForm() {
  const record = await Detail({ deviceId: route.query.deviceId });
  device.value = record.device ? record.device : {};
  console.log('device.value', device.value);

  // 确保网关信息被正确设置
  if (record.device) {
    device.value.gatewayKey = record.device.gatewayKey || '';
    device.value.gatewayName = record.device.gatewayName || '';
    device.value.gatewayId = record.device.gatewayId || 0;
    console.log('网关信息:', {
      gatewayKey: device.value.gatewayKey,
      gatewayName: device.value.gatewayName,
      gatewayId: device.value.gatewayId
    });
    console.log('完整的设备数据:', record.device);
  }

  product.value = record.product ? record.product : {};
  if (product.value && product.value.thingsModelsJson && product.value.thingsModelsJson != '') {
    tsl.value = JSON.parse(product.value.thingsModelsJson);
  } else {
    tsl.value = JSON.parse("{}");
  }

  properties.value = record.properties ? record.properties : {};
  await formApi.setValues(device.value);
  await formApi.setValues({
    deviceType: String(product.value.deviceType),
    transport: String(product.value.transport),
    channelType: String(product.value.channelType),
  });
  if (isUpdate.value) {
    formApi.setState({
      commonConfig: {
        componentProps: {
          readonly: false,
          "only-read": false,
          disabled: false,
        }
      }
    });
  } else {
    formApi.setState({
      commonConfig: {
        componentProps: {
          readonly: true,
          "only-read": true,
          disabled: true,
        }
      }
    }
    );
  }
}

// async function handleEditConfirm() {
//   try {
//     const { valid } = await formApi.validate();
//     if (!valid) {
//       return;
//     }
//     const data = cloneDeep(await formApi.getValues());
//     data.publishStatus = 1;
//     await (Edit(data));
//     await handleEditCancel();
//   } catch (error) {
//     console.error(error);
//   }
// }

// import { cloneDeep, } from '@vben/utils';
// async function handleEditCancel() {
//   isUpdate.value = false;
//   reloadEditForm();
// }

// function handleEdit() {
//   isUpdate.value = true;
//   reloadEditForm();
// }
const handleAddUser = () => {
  // 设置要传递给 edit.vue 的数据，比如产品Key、设备Key等
  editDrawerApi.setData({
    productKey: product.value.productKey,
    deviceKey: device.value.deviceKey,
    update: false,  // 新增模式
    view: false,    // 非查看模式
    id: null        // 新增时没有ID
  });
  // 打开抽屉
  editDrawerApi.open();
};

async function exportProductModel() {
  const data = await ExportModel({ productId: product.value.productId });
  console.log('data', data);
  downloadByData(data, "物模型-" + product.value.productKey + ".json");
}

function onTabChange(key: any) {
  console.log('onTabChange', key);
  currentModelType.value = key;
  if (currentTab.value == 'b') {
    refreshIotProductModel(key);
  }
}

function refreshIotProductModel(key: any) {
  switch (key) {
    case 'property':
      if (iotProductModel.value) {
        iotProductModel.value.reload();
      }
      break;
    case 'function':
      if (iotProductModelFuntion.value) {
        iotProductModelFuntion.value.reload();
      }
      break;
    case 'event':
      iotProductModelEvent.value.reload();
      break;
    case 'tag':
      iotProductModelTag.value.reload();
      break;
  }
}
const toProductDetail = (productId: number) => {
  router.push(`/device/productDetail?productId=${productId}`);
}

// 跳转到网关设备详情页面
const toGatewayDetail = async (gatewayId: number) => {
  if (!gatewayId || gatewayId === 0) {
    console.warn('网关ID无效:', gatewayId);
    message.warning('网关ID无效，无法跳转');
    return;
  }

  console.log('跳转到网关设备详情页面，gatewayId:', gatewayId);

  // 添加时间戳确保创建新的标签页
  const timestamp = Date.now();
  const targetPath = `/device/deviceDetail?deviceId=${gatewayId}&t=${timestamp}`;
  console.log('目标路径:', targetPath);

  // 手动添加新的标签页
  const newTab = {
    fullPath: targetPath,
    path: '/device/deviceDetail',
    name: 'DeviceDetail',
    query: { deviceId: gatewayId.toString(), t: timestamp.toString() },
    params: {},
    hash: '',
    meta: {
      title: '设备详情',
      icon: 'lucide:monitor',
      keepAlive: true,
      newTabTitle: `网关设备详情-${gatewayId}`
    },
    matched: [],
    redirectedFrom: undefined
  };

  console.log('添加新标签页:', newTab);
  tabbarStore.addTab(newTab as any);

  // 跳转到新页面
  await router.push(targetPath);
  console.log('✅ 已跳转到网关设备详情页面');
}
</script>

<template>
  <Page class="h-full overflow-y-auto">
    <Card class="mb-4">
      <label class="ml-2 text-xl">{{ device.deviceName }}</label>
      <label class="ml-4">设备编号: {{ device.deviceKey }} </label>
      <label class="ml-4">设备状态: {{getDictOptions(DictEnum.DEVICE_STATE).find((item: any) => item.value ==
        device.deviceState)?.label}} </label>
      <label class="ml-4">所属产品: </label>
      <Button class="p-0" type="link" @click="toProductDetail(product.productId)">{{ product.productName }}</Button>
      <span class="ml-4" v-if="device.gatewayName || device.gatewayKey">
        <label>网关设备: </label>
        <!-- 如果有有效的网关ID，显示为可点击链接 -->
        <Button v-if="device.gatewayId && device.gatewayId !== 0" class="p-0" type="link"
          @click="toGatewayDetail(device.gatewayId)">
          {{ device.gatewayName || device.gatewayKey || '未配置' }}
        </Button>
        <!-- 如果没有有效的网关ID，显示为普通文本 -->
        <span v-else class="text-gray-500">
          {{ device.gatewayName || device.gatewayKey || '未配置' }}
        </span>
      </span>
    </Card>
    <Card style="height:calc(100vh - 200px);" class="overflow-y-auto">
      <Tabs v-model:activeKey="currentTab">
        <TabPane key="a" tab="实例信息">
          <label style="font-size: 18px;">设备信息 </label>
          <Divider />
          <BasicForm class="gap-[8px] h-full"></BasicForm>
        </TabPane>
        <TabPane key="a1" tab="运行状态">
          <PropertiesTemplate v-if="currentTab == 'a1'" :key="currentTab + '-' + refreshKey"
            :tslProperties="tsl.properties" :productKey="device.productKey" :properties="properties"
            :deviceKey="device.deviceKey" :tenantId="device.tenantId" :productTransport="product.transport" />
        </TabPane>
        <TabPane key="b" tab="物模型">
          <Tabs class="pl-2 pr-2 border-[1px] border-solid border-[#d9d9d9] rounded-[4px]"
            v-model:activeKey="currentModelType" @tabChange="onTabChange">
            <TabPane key="property" tab="属性定义" class="gap-[8px] h-[800px]">
              <IotProductModel v-if="currentModelType == 'property' && product.productKey !== ''" ref="iotProductModel"
                :product-key="product.productKey" type="property" title="属性定义" :published="true" />
            </TabPane>
            <TabPane key="function" tab="功能定义" class="gap-[8px] h-[800px]">
              <IotProductModel v-if="currentModelType == 'function' && product.productKey !== ''"
                ref="iotProductModelFuntion" :product-key="product.productKey" type="function" title="功能定义"
                :published="true" />
            </TabPane>
            <TabPane key="event" tab="事件定义" class="gap-[8px] h-[800px]">
              <IotProductModel v-if="currentModelType == 'event' && product.productKey !== ''"
                ref="iotProductModelEvent" :product-key="product.productKey" type="event" title="事件定义"
                :published="true" />
            </TabPane>
            <TabPane key="tag" tab="标签定义" class="gap-[8px] h-[800px]">
              <IotProductModel v-if="currentModelType == 'tag' && product.productKey !== ''" ref="iotProductModelTag"
                :product-key="product.productKey" type="tag" title="标签定义" :published="true" />
            </TabPane>
            <TabPane key="expand" tab="扩展设置" class="gap-[8px] h-[800px]">
              <div v-if="currentModelType == 'expand'" ref="iotProductModelExpand" title="扩展设置" />
            </TabPane>
            <template #rightExtra class="flex items-center">
              <Button v-if="product.productKey !== ''" @click="exportProductModel"
                style="display: inline-flex; align-items: center;">导出物模型</Button>
            </template>
          </Tabs>
        </TabPane>
        <TabPane v-if="product.deviceType == '2'" key="b1" tab="子设备">
          <IotDeviceRelation :parentProductKey="device.productKey" :parentDeviceKey="device.deviceKey" />
        </TabPane>
        <TabPane v-if="product.transport == 2 || product.transport == 4" key="b2" tab="采集任务" @tabChange="onTabChange">
          <IotPollTask class="p-0 m-0" targetType="1" :productKey="device.productKey" :deviceKey="device.deviceKey" />
        </TabPane>
        <TabPane key="c" tab="设备功能">
          <DeviceFunction :productKey="device.productKey" :deviceKey="device.deviceKey" />
        </TabPane>
        <TabPane key="d" tab="日志管理">
          <IotDeviceLog class="p-0 m-0" :deviceKey="device.deviceKey" :tenantId="device.tenantId" :fromCache="false" />
        </TabPane>
        <TabPane key="e" tab="报警记录">
          <Alarmlog class="p-0 m-0" :productKey="device.productKey" :deviceKey="device.deviceKey" />
        </TabPane>
        <TabPane key="f" tab="无效数据">
          <Feiqirizhi class="p-0 m-0" :productKey="device.productKey" :deviceKey="device.deviceKey" />
        </TabPane>
        <TabPane key="g" tab="物联网卡">

        </TabPane>
        <TabPane key="h" tab="设备诊断">

        </TabPane>
        <TabPane key="i" tab="报警用户">
          <Tabs class="pl-2 pr-2 border-[1px] border-solid border-[#d9d9d9] rounded-[4px]"
            v-model:activeKey="currentModelType" @tabChange="onAlarmUserTabChange">
            <TabPane key="property" tab="设备报警用户" class="gap-[8px] h-[800px]">
              <Baojingyonghu v-if="currentModelType == 'property' && product.productKey !== ''" ref="baojingyonghuRef"
                :product-key="product.productKey" :deviceKey="device.deviceKey" type="property" :published="true" />
            </TabPane>
            <TabPane key="function" tab="设备绑定终端用户" class="gap-[8px] h-[800px]">
              <IotDevicebindConsumer v-if="currentModelType == 'function'" ref="iotDevicebindConsumerRef" :deviceKey="device.deviceKey" />
            </TabPane>
            <TabPane key="event" tab="项目接警用户" class="gap-[8px] h-[800px]">

            </TabPane>
            <template #rightExtra class="flex items-center">
              <Button v-if="product.productKey !== ''" @click="handleAddUser"
                style="display: inline-flex; align-items: center;">
                新增用户
              </Button>
            </template>
          </Tabs>
        </TabPane>
        <TabPane key="j" tab="视频监控">
          <VideoMonitoring v-if="currentTab == 'j'" :device-key="device.deviceKey"
            :device-id="String(device.deviceId || '')" :device="device" :product="product" />
        </TabPane>
      </Tabs>
    </Card>

    <!-- 新增用户抽屉 -->
    <EditDrawer @reload="refresh" />
  </Page>
</template>
