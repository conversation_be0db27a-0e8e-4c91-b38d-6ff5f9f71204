import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import { getPopupContainer } from '@vben/utils';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { z } from '@vben/common-ui';
import { renderPopoverMemberSumma, type MemberSumma } from '#/utils';

/** 表格数据 */
export class State {
  public unionId = 0; // 组合设备ID
  public unionKey = ''; // 组合设备标识
  public unionName = ''; // 组合设备名称
  public imgUrl = null; // 图片地址
  public status = 0; // 状态：0=正常，1=停用
  public modelValue = null; // 属性值
  public tenantId = ''; // 租户ID
  public deptId = 0; // 所属机构
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public createdBySumma?: null | MemberSumma = null; // 创建者摘要信息
  public createdAt = ''; // 创建时间
  public updatedBy = 0; // 更新者
  public updatedBySumma?: null | MemberSumma = null; // 更新者摘要信息
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedBySumma?: null | MemberSumma = null; // 删除人摘要信息
  public deletedAt = ''; // 删除时间
  public remark = null; // 备注
  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}
/** 表格数据实例创建函数 */
export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    /** 非空,类型为State,深度克隆 */
    if (state instanceof State) {
      return cloneDeep(state);
    }
    /** 非空,object类型,通过参数创建新State */
    return new State(state);
  }
  /** 空,创建新State */
  return new State();
}

// 表单验证规则

/** 表格顶部搜索功能 */
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'unionKey',
    component: 'Input',
    label: '设备标识',
    componentProps: {
      placeholder: '请输入设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  }, {
    fieldName: 'unionName',
    component: 'Input',
    label: '设备名称',
    componentProps: {
      placeholder: '请输入设备名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  }
];

/** 表格列配置 */
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  // 组合设备ID
  {
    title: '编号',
    field: 'unionId',
    align: 'center',
    width: 80,
  },
  // 图片地址
  {
    title: '图片',
    field: 'imgUrl',
    slots: { default: 'imgUrl' },
    width: 50,
  },
  //  组合设备标识
  {
    title: '组合设备标识',
    field: 'unionKey',
    align: 'center',
    width: -1,
  },
  // 组合设备名称
  {
    title: '组合设备名称',
    field: 'unionName',
    align: 'center',
    width: -1,
  },
  // 是否启用
  {
    title: '是否启用',
    field: 'status',
    align: 'center',
    width: 80,
    slots: { default: 'status' },
  },
  // 备注
  {
    title: '备注',
    field: 'remark',
    align: 'center',
    width: -1,
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];

// 表格列接口
/** 表格列类型 */
export interface RowType {
  unionId: number;
  imgUrl: string;
  unionKey: string;
  unionName: string;
  status: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'unionId', label: '组合设备ID' },
  { field: 'unionKey', label: '组合设备标识' },
  { field: 'unionName', label: '组合设备名称' },
  { field: 'imgUrl', label: '图片地址' },
  { field: 'status', label: '状态', render: (val) => val == 0 ? '正常' : '停用' },
  { field: 'modelValue', label: '属性值' },
  { field: 'tenantId', label: '租户ID' },
  { field: 'deptId', label: '所属机构' },
  { field: 'createdDept', label: '创建部门' },
  { field: 'createdBy', label: '创建者' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'updatedBy', label: '更新者' },
  { field: 'updatedAt', label: '更新时间' },
  { field: 'deletedBy', label: '删除人' },
  { field: 'deletedAt', label: '删除时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'unionId',
    component: 'Input',
    label: '组合设备ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'unionKey',
    component: 'Input',
    label: '组合设备标识',
    componentProps: {
      placeholder: '请输入组合设备标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'unionName',
    component: 'Input',
    label: '组合设备名称',
    componentProps: {
      placeholder: '请输入组合设备名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'deptId',
    component: 'TreeSelect',
    label: '所属机构',
    componentProps: {
      getPopupContainer,
    }, rules: 'selectRequired',
  },
  {
    fieldName: 'status',
    component: 'Switch',
    label: '是否启用',
    defaultValue: false,
    componentProps: {
      style: {
        width: '20px',
      },
      placeholder: '请选择状态',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired',
  }, {
    fieldName: 'imgUrl',
    component: 'Input',
    label: '设备图片',
  },
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注',
    componentProps: {
      placeholder: '请输入备注',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
];