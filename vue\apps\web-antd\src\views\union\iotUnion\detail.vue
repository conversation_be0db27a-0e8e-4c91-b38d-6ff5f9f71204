<template>
  <Page auto-content-height class="h-full overflow-y-auto">
    <Card class="mb-4">
      <Image :url="union.imgUrl"></Image>
      <label class="ml-2 text-xl">{{ union.unionName }}</label>
      <label class="ml-6">组合设备标识：{{ union.unionKey }}</label>
      <label class="ml-6">设备状态：{{ union.status == "0" ? "正常" : "停用" }}</label>
    </Card>
    <Card>
      <Tabs v-model:activeKey="currentTab">
        <TabPane key="a" tab="设备配置">
          <DeviceConfig :unionKey="route.query.unionId"></DeviceConfig>
        </TabPane>
        <TabPane key="b" tab="录入型变量">
          <InputType :unionKey="route.query.unionId"></InputType>
        </TabPane>
        <TabPane key="c" tab="运算型变量">
          <CalculateType></CalculateType>
        </TabPane>
      </Tabs>
    </Card>
  </Page>
</template>
<script setup lang='ts'>
import DeviceConfig from "./deviceConfig/index.vue"
import InputType from "./inputType/index.vue"
import CalculateType from "./calculateType/index.vue"
import { Page } from '@vben/common-ui';
import { Card, Tabs, TabPane, Image } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import { onMounted, ref, watch } from 'vue'
import { View } from "#/api/union/iotUnion";
/** 路由实例 */
const route = useRoute();
/** 未知 */
const isUpdate = ref(false);
/** 组合设备详情数据实例 */
const union = ref({
  unionId: route.query.unionId,
  unionKey: "",
  unionName: "",
  imgUrl: "",
  status: "",
  modelValue: "",
  tenantId: "",
  deptId: 0,
  createdDept: 0,
  createdBy: 0,
  createdAt: "",
  updatedBy: 0,
  updatedAt: "",
  deletedBy: 0,
  deletedAt: "",
  remark: "",
  createdBySumma: {
    userId: 0,
    userName: "",
    nickName: "",
    avatar: "",
    tenantId: "",
    deptId: 0
  },
  updatedBySumma: {
    userId: 0,
    userName: "",
    nickName: "",
    avatar: "",
    tenantId: "",
    deptId: 0
  },
  deletedBySumma: {
    userId: 0,
    userName: "",
    nickName: "",
    avatar: "",
    tenantId: "",
    deptId: 0
  }
})
/** 当前tab页 */
const currentTab = ref('b');
/** 刷新数据函数 */
async function refresh() {
  isUpdate.value = false;
  // 重载数据
  const res = await View({ unionId: route.query.unionId });
  console.log("id:" + route.query.unionId)
  /** 将数据载入至变量 */
  union.value = { ...res };
}
/** 自动刷新数据 */
onMounted(async () => {
  refresh()
})
// }
</script>

<style scoped></style>