import { h, ref } from 'vue';
import { Tag } from 'ant-design-vue';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { renderDict, renderDictTags } from '#/utils';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
//关联场景的表格列以及接口信息
export const scenecolumns: VxeGridProps['columns'] = [
  {
    title: '编号',
    field: 'sceneId',
    align: 'center',
    width: 100,
  },
  {
    title: '场景名称',
    field: 'sceneName',
    align: 'center',
    width: -1,
  },
  {
    title: '状态',
    field: 'status',
    align: 'center',
    width: -1,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
        if (found) {
          return found;
        }
        return row.status;
      },
    },
  },
  {
    title: '触发类型',
    field: 'triggerType',
    align: 'center',
    width: -1,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.triggerType, DictEnum.SCENE_TRIGGER_TYPE);
        if (found) {
          return found;
        }
        return row.triggerType;
      },
    },
  },
  {
    title: '执行方式',
    field: 'executeMode',
    align: 'center',
    width: -1,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.executeMode, DictEnum.SCENE_EXECUTE_MODE);
        if (found) {
          return found;
        }
        return row.executeMode;
      },
    },
  },
  { title: '操作', width: 120, slots: { default: 'Newaction' } },

];
// 关联场景的表格列接口
export interface SceneRowType {
  sceneId: number;
  sceneName: string;
  status: string;
  triggerType: string; // 触发条件
  executeMode: number; // 执行方式
};

//消息通知的表格列以及接口
export const notifycolumns: VxeGridProps['columns'] = [
  {
    title: '模板名称',
    field: 'templateName',
    align: 'center',
    width: -1,
  },
  {
    title: '状态',
    field: 'status',
    align: 'center',
    width: -1,
    slots: {
      default: ({ row }) => {
        let found = renderDict(row.status, DictEnum.SYS_NORMAL_DISABLE);
        if (found) {
          return found;
        }
        return row.status;
      },
    },
  },
  {
    title: '渠道类型',
    field: 'channelType',
    align: 'center',
    width: -1,
    //这个是多个选项，（1=短信 2=微信 3=语音 4=邮箱 5=钉钉 6=MQTT） notify_channel_type
    slots: {
      default: ({ row }) => renderDict(row.channelType, DictEnum.NOTIFY_CHANNEL_TYPE),
    },
  },
  {
    title: '业务类型',
    field: 'businessType',
    align: 'center',
    width: -1,
    //（1=设备报警 2=验证码 3=营销通知）
    slots: {
      default: ({ row }) => renderDict(row.channelType, DictEnum.NOTIFY_BUSINESS_TYPE),
    },
  },
  {
    title: '服务商',
    field: ' serviceType',
    align: 'center',
    width: -1,
    slots: {
      default: ({ row }) => {
        // 根据渠道类型动态选择服务商字典
        let dictType = DictEnum.NOTIFY_PROVIDER_SMS;
        if (row.channelType) {
          switch (row.channelType) {
            case 1: // 短信
            case 3: // 语音
              dictType = DictEnum.NOTIFY_PROVIDER_SMS;
              break;
            case 2: // 微信
              dictType = DictEnum.NOTIFY_PROVIDER_WECHAT;
              break;
            case 4: // 邮箱
              dictType = DictEnum.NOTIFY_PROVIDER_EMAIL;
              break;
            case 5: // 钉钉
              dictType = DictEnum.NOTIFY_PROVIDER_DING;
              break;
            case 6: // MQTT
              dictType = DictEnum.NOTIFY_PROVIDER_MQTT;
              break;
          }
        }
        return renderDict(row.serviceType, dictType);
      }
    },
  },
  { title: '操作', width: 120, slots: { default: 'Newaction' } },

];
// 消息通知的表格列接口
export interface NotifyRowType {
  id: number;
  templateName: string;
  status: string;
  channelType: string; // 渠道类型
  businessType: number; // 业务类型
  serviceType: string; // 服务商
};



// 之前的代码------------------------------------
export class State {
  public alarmConfigId = 0; // 报警配置ID
  public alarmName = ''; // 报警名称
  public status = 0; // 状态
  public alarmLevel = null; // 报警级别
  public createdAt = ''; // 创建时间
  public remark = null; // 备注信息



  public notifyUserType = null; // 报警通知用户
  public confirmUserType = null; // 报警处理用户
  public tenantId = ''; // 租户ID
  public createdDept = 0; // 创建部门
  public createdBy = 0; // 创建者
  public updatedBy = 0; // 更新者
  public updatedAt = ''; // 更新时间
  public deletedBy = 0; // 删除人
  public deletedAt = ''; // 删除时间

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表单验证规则

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'alarmName',
    component: 'Input',
    label: '报警名称',
    componentProps: {
      placeholder: '请输入报警名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },

  {
    fieldName: 'alarmLevel',
    component: 'Select',
    label: '报警级别',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择报警级别',
      options: getDictOptions(DictEnum.ALARM_LEVEL),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 60,
  },
  {
    title: '编号',
    field: 'alarmConfigId',
    align: 'center',
    width: 100,
  },
  {
    title: '报警名称',
    field: 'alarmName',
    align: 'center',
    width: -1,
  },
  {
    title: '状态',
    field: 'status',
    align: 'center',
    width: 120,
    slots: { default: 'status' },
  },
  {
    title: '报警级别',
    field: 'alarmLevel',
    align: 'center',
    width: 150,
    slots: {
      default: ({ row }) => {
        const level = row.alarmLevel;
        // 你可以根据 level 的值返回不同颜色的标签
        let color = 'default';
        let text = '未知';

        switch (level) {
          case 1:
            color = 'green';
            text = '提醒通知';
            break;
          case 2:
            color = 'orange';
            text = '轻微问题';
            break;
          case 3:
            color = 'red';
            text = '严重告警';
            break;
        }

        return h(Tag, { color }, () => text);
      },
    },
  },
  {
    title: '创建时间',
    field: 'createdAt',
    align: 'center',
    width: 200,
  },
  //添加一列备注信息
  {
    title: '备注信息',
    field: 'remark',
    align: 'center',
    width: -1,
    slots: {
      default: ({ row }) => {
        return row.remark || '-';
      }
    },
  },
  { title: '操作', width: 200, align: 'center', slots: { default: 'action' } },
];

// 表格列接口
export interface RowType {
  alarmConfigId: number;
  alarmName: string;
  alarmLevel: number;
  status: string;
  createdAt: string;
  remark: string;

  notifyUserType: string;
  confirmUserType: string;
  tenantId: string;
  createdDept: number;
  createdBy: number;
  updatedBy: number;
  updatedAt: string;
  deletedBy: number;
  deletedAt: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'alarmConfigId', label: '编号' },
  { field: 'alarmName', label: '报警名称' },
  {
    field: 'status',
    label: '状态',
    render(val: any, _) {
      return renderDict(val, DictEnum.SYS_NORMAL_DISABLE);
    },
  },
  {
    field: 'alarmLevel',
    label: '报警级别',
    render(val: any, _) {
      return renderDict(val, DictEnum.ALARM_LEVEL);
    },
  },

  {
    field: 'notifyUserType',
    label: '报警通知用户',
    render(_, row: any) {
      let values: string[] = [];
      try {
        values = JSON.parse(row.notifyUserType || '[]');
      } catch {
        values = [];
      }
      return renderDictTags(
        values,
        getDictOptions('notify_user_type'),
        true,
        4
      );
    }
  },
  {
    field: 'confirmUserType',
    label: '报警处理用户',
    render(_, row: any) {
      let values: string[] = [];
      try {
        values = JSON.parse(row.confirmUserType || '[]');
      } catch {
        values = [];
      }
      return renderDictTags(
        values,
        getDictOptions('confirm_user_type'),
        true,
        4
      );
    }
  },
  //关联场景
  {
    field: 'sceneList',
    label: '关联场景',
    render(_, row) {
      const list = row.alarmScenes || [];
      if (!list.length) return '无';
      return list.map((item: { sceneName: string }) => item.sceneName).join('，');
    }
  },
  {
    field: 'notifyList',
    label: '消息通知',
    render(_, row) {
      const list = row.alarmTemplates || [];
      if (!list.length) return '无';
      return list.map((item: { templateName: string }) => item.templateName).join('，');
    }
  },
  { field: 'createdAt', label: '创建时间' },
  { field: 'updatedAt', label: '更新时间' },
  {
    field: 'remark',
    label: '备注信息',
    render(val: any, _) {
      return val || '-';
    },
  },

];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'alarmConfigId',
    component: 'Input',
    label: '报警配置ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'alarmName',
    component: 'Input',
    label: '报警名称',
    //想让其占据一列
    formItemClass: 'col-span-1',
    componentProps: {
      placeholder: '请输入报警名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  {
    fieldName: 'status',
    component: 'Select',
    label: '状态',
    formItemClass: 'col-span-2',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择状态',
      options: getDictOptions('sys_normal_disable'),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired'
  },
  {
    fieldName: 'alarmLevel',
    component: 'Select',
    label: '报警级别',
    formItemClass: 'col-span-1',

    defaultValue: null,
    componentProps: {
      placeholder: '请选择报警级别',
      options: getDictOptions(DictEnum.ALARM_LEVEL),
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired'
  },
  {
    fieldName: 'remark',
    component: 'Input',
    label: '备注信息',
    formItemClass: 'col-span-2',
    componentProps: {
      placeholder: '请输入备注信息',
      onUpdateValue: (e: any) => {
        console.log(e);
      }
    },
    rules: null
  },
  {
    fieldName: 'notifyUserTypeList',
    component: 'CheckboxGroup',
    //占据一整行
    label: '报警通知用户',
    componentProps: {
      placeholder: '请选择报警通知用户',
      options: getDictOptions('notify_user_type'),
      onUpdateChecked: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
  {
    fieldName: 'confirmUserTypeList',
    component: 'CheckboxGroup',
    //占据一整行
    formItemClass: 'col-span-2',
    label: '报警处理用户',
    componentProps: {
      placeholder: '请选择报警处理用户',
      options: getDictOptions('confirm_user_type'),
      onUpdateChecked: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },

];
