<template>
  <Grid>
    <template #toolbar-tools>
      <Space>
        <Button type="primary" @click="handleAdd">新增</Button>
        <Button>删除</Button>
      </Space>
    </template>
    <!-- 表格右侧功能 -->
    <template #action="{ row }">
      <Flex>
        <Button>修改</Button>
        <Button>删除</Button>
      </Flex>
    </template>
  </Grid>
  <EditDrawer></EditDrawer>
</template>

<script setup lang='ts' name=''>
import { Button, Space, Flex } from 'ant-design-vue';
import { useVbenVxeGrid, type VxeTableGridOptions } from '@vben/plugins/vxe-table';
import { } from 'vue'
import { columns, querySchema } from '../model';
import { useVbenDrawer, type VbenFormProps } from '@vben/common-ui';
import editDrawer from './edit.vue';

/** 表格配置 */
const gridOptions: VxeTableGridOptions = {
  checkboxConfig: {
    highlight: true,
  },
  rowConfig: {
    keyField: 'id',
  },
  columns: columns,
  exportConfig: {},
  // height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    // ajax: {
    //   query: async ({ page }, formValues) => {
    //     return await List({
    //       page: page.currentPage,
    //       pageSize: page.pageSize,
    //       ...formValues,
    //     });
    //   },
    // },
  },
  toolbarConfig: {
    /** 列筛选 */
    // custom: true,
    /** 导出 */
    export: true,
    /** 刷新 */
    refresh: true,
    /** 未知 */
    // resizable: true,
    /** 未知 */
    // search: true,
    /** 全屏 */
    // zoom: true,
  },
}
/** 表格表单配置 */
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
/** 表格实例 */
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions
});

/** 新增/修改实例 */
const [EditDrawer, editDrawerApi] = useVbenDrawer({
  connectedComponent: editDrawer,
});
function handleAdd() {
  editDrawerApi.setData({ update: false});
  editDrawerApi.open();
}
</script>

<style scoped></style>
