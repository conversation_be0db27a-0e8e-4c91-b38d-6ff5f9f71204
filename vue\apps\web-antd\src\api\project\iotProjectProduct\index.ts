import { requestClient } from '#/api/request';

// 获取项目产品关联表列表
export function List(params:any) {
  return requestClient.get<any>('project/iotProjectProduct/list', { params });
}

// 删除/批量删除项目产品关联表
export function Delete(params:any) {
  return requestClient.post<any>('project/iotProjectProduct/delete', { ...params });
}

// 添加/编辑项目产品关联表
export function Edit(params:any) {
  return requestClient.post<any>('project/iotProjectProduct/edit', { ...params });
}

// 获取项目产品关联表指定详情
export function View(params:any) {
  return requestClient.get<any>('project/iotProjectProduct/view', { params });
}

// 导出项目产品关联表
export function Export(params:any) {
  return requestClient.post<Blob>('/project/iotProjectProduct/export',  { ...params } , {
    headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' },
    responseType: 'blob',
  });
}