<script setup lang="ts">
import { h, reactive, ref, computed, onMounted, nextTick, watch } from 'vue';
import { Button, message, Tag, Modal, Popconfirm, Switch } from 'ant-design-vue';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { MdiDelete } from '@vben/icons';
import { List as ListCoil, Delete as DeleteCoil } from '#/api/device/iotModbusCoil';
import { columns as coilColumns, type RowType as CoilRowType } from './coilmodel';
import coilEditModal from './coiledit.vue';

import { List as ListReg, Delete as DeleteReg } from '#/api/device/iotModbusReg';
import { columns as regColumns, type RowType as RegRowType } from './regmodel';
import regEditModal from './regedit.vue';
import { List as ListProductProperties } from '#/api/device/iotProductModel';
const props = defineProps<{
  productKey: string;
  published: boolean;
}>();

const boolProperties = ref<any[]>([]);
const numberProperties = ref<any[]>([]);

const loadProductProperties = async () => {
  const res = await ListProductProperties({ productKey: props.productKey, page: 1, pageSize: 1000, type: 'property' });
  for (const property of res.items) {
    boolProperties.value.push({
        label: property.modelKey,
        value: property.modelKey
      });
    numberProperties.value.push({
      label: property.modelKey,
      value: property.modelKey
    });
  }
}

onMounted(() => {
  loadProductProperties();
});

const coilGridOptions: VxeTableGridOptions<CoilRowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'modbusId',
  },
  rowConfig: {
    keyField: 'modbusId',
  },
  columns: coilColumns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await ListCoil({
          productKey: props.productKey,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar-tools-coil',
    },
    custom: false,
    export: false,
    refresh: true,
    resizable: false,
    search: true,
    zoom: false,
  },
};
const coilGridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCoilCheckboxChange,
  checkboxAll: handleCoilCheckboxChange,
};
const coilCheckboxChecked = ref(false);
function handleCoilCheckboxChange() {
  coilCheckboxChecked.value = coilGridApi.grid.getCheckboxRecords().length > 0;
}
const [CoilGrid, coilGridApi] = useVbenVxeGrid({
  gridClass: 'h-[300px] p-0',
  gridOptions: coilGridOptions,
  gridEvents: coilGridEvents,
});

const [CoilEditModal, coilEditModalApi] = useVbenModal({
  connectedComponent: coilEditModal,
});
function handleAddCoil() {
  coilEditModalApi.setData({ update: false, view: false , properties: boolProperties.value, productKey: props.productKey });
  coilEditModalApi.open();
}
function handleEditCoil(row: CoilRowType) {
  coilEditModalApi.setData({ id: row.modbusId, update: true, view: false, properties: boolProperties.value, productKey: props.productKey });
  coilEditModalApi.open();
}
async function handleDeleteCoil(row: CoilRowType) {
  await DeleteCoil({ modbusId: [row.modbusId] });
  message.success("删除成功");
  await handleRefreshCoil();
}
async function handleRefreshCoil() {
  await coilGridApi.query();
}
function handleMultiDeleteCoil() {
  const rows = coilGridApi.grid.getCheckboxRecords();
  const ids: string[] = [];
  for (const row of rows) {
    ids.push(row.modbusId);
  }
  if (ids.length === 0) {
    message.error('请至少选择一项要删除的数据');
    return;
  }
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await DeleteCoil({ modbusId: ids });
      message.success("删除成功");
      await handleRefreshCoil();
    },
  });
}
 
//==============================================后面是数据寄存器========================================================
const regGridOptions: VxeTableGridOptions<RegRowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'modbusId',
  },
  rowConfig: {
    keyField: 'modbusId',
  },
  columns: regColumns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await ListReg({
          productKey: props.productKey,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar-tools-coil',
    },
    custom: false,
    export: false,
    refresh: true,
    resizable: false,
    search: true,
    zoom: false,
  },
};
const regGridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleRegCheckboxChange,
  checkboxAll: handleRegCheckboxChange,
};
const regCheckboxChecked = ref(false);
function handleRegCheckboxChange() {
  regCheckboxChecked.value = regGridApi.grid.getCheckboxRecords().length > 0;
}
const [RegGrid, regGridApi] = useVbenVxeGrid({
  gridClass: 'h-[300px] p-0',
  gridOptions: regGridOptions,
  gridEvents: regGridEvents,
});

const [RegEditModal, regEditModalApi] = useVbenModal({
  connectedComponent: regEditModal,
});
function handleAddReg() {
  regEditModalApi.setData({ update: false, view: false,  properties: numberProperties.value, productKey: props.productKey});
  regEditModalApi.open();
}
function handleEditReg(row: RegRowType) {
  regEditModalApi.setData({ id: row.modbusId, update: true, view: false, properties: numberProperties.value, productKey: props.productKey });
  regEditModalApi.open();
}
async function handleDeleteReg(row: RegRowType) {
  await DeleteReg({ modbusId: [row.modbusId] });
  message.success("删除成功");
  await handleRefreshReg();
}
async function handleRefreshReg() {
  await regGridApi.query();
}
function handleMultiDeleteReg() {
  const rows = regGridApi.grid.getCheckboxRecords();
  const ids: string[] = [];
  for (const row of rows) {
    ids.push(row.modbusId);
  }
  if (ids.length === 0) {
    message.error('请至少选择一项要删除的数据');
    return;
  }
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await DeleteReg({ modbusId: ids });
      message.success("删除成功");
      await handleRefreshReg();
    },
  });
}
// onMounted(async () => {
//   await handleRefreshCoil();
// });
</script>
<template>
  <Page auto-content-height>
    <div class="mb-6">
        <div class="text-2xl mb-2">Modbus 寄存器设置</div>
        <label class="text-sm text-gray-500">当产品的通讯协议选择 ModbusRTU 时，可设置设备 Modbus 寄存器地址，用于设备属性和 Modbus 消息之间的自动转换。</label>
    </div> 
    <div class="mb-6">
      <label class="font-bold text-lg">IO 寄存器</label><br/>
      <label class="text-sm text-gray-500">IO 寄存器包括线圈、离散寄存器，可以和开关量（Bool）属性绑定。 {{ props.published }}</label>
      <CoilGrid>
        <template #toolbar-tools-coil>
          <Button v-if="!props.published" class="mr-2 flex items-center " type="primary"  @click="handleAddCoil"
            v-access:code="'cpm:device:iotModbusCoil:edit'">
            添加IO寄存器
          </Button>
          <Button v-if="!props.published" class="mr-2 flex items-center" type="primary" :disabled="!coilCheckboxChecked" :icon="h(MdiDelete)"
            @click="handleMultiDeleteCoil" v-access:code="'cpm:device:iotModbusCoil:delete'">
            删除IO寄存器
          </Button>
        </template>
        <template #action="{ row }">
          <div class="flex items-center" v-if="!props.published">
            <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEditCoil(row)"
              v-access:code="'cpm:device:iotModbusCoil:edit'">
              修改
            </Button>
            <AccessControl :codes="['cpm:device:iotModbusCoil:delete']" type="code">
              <Popconfirm title="确定删除吗？" :get-popup-container="(triggerNode: HTMLElement) => getVxePopupContainer(triggerNode, 'delete-coil'+row.modbusId)" placement="left"
                @confirm="handleDeleteCoil(row)">
                <Button :id="'delete-coil'+row.modbusId"  class="mr-2 border-none p-0" :block="false" type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </AccessControl>
          </div>
        </template>
      </CoilGrid>
      <CoilEditModal @reload="handleRefreshCoil" />
    </div>
    <div>
      <label class="font-bold text-lg">数据寄存器</label><br/>
      <label class="text-sm text-gray-500">数据寄存器包括输入寄存器、保持寄存器，可以和数值型（Number）属性绑定。</label>
      <RegGrid>
        <template #toolbar-tools-coil >
          <Button v-if="!props.published" class="mr-2 flex items-center " type="primary"  @click="handleAddReg"
            v-access:code="'cpm:device:iotModbusReg:edit'">
            添加数据寄存器
          </Button>
          <Button v-if="!props.published" class="mr-2 flex items-center" type="primary" :disabled="!regCheckboxChecked" :icon="h(MdiDelete)"
            @click="handleMultiDeleteReg" v-access:code="'cpm:device:iotModbusReg:delete'">
            删除数据寄存器
          </Button>
        </template>
        <template #action="{ row }">
          <div class="flex items-center" v-if="!props.published">
            <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEditReg(row)"
              v-access:code="'cpm:device:iotModbusReg:edit'">
              修改
            </Button>
            <AccessControl :codes="['cpm:device:iotModbusReg:delete']" type="code">
              <Popconfirm title="确定删除吗？" :get-popup-container="(triggerNode: HTMLElement) => getVxePopupContainer(triggerNode, 'delete-reg'+row.modbusId)" placement="left"
                @confirm="handleDeleteReg(row)">
                <Button :id="'delete-reg'+row.modbusId"   class="mr-2 border-none p-0" :block="false" type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </AccessControl>
          </div>
        </template>
      </RegGrid>
      <RegEditModal @reload="handleRefreshReg" />
    </div>
  </Page>
</template>