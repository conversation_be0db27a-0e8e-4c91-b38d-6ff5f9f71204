<script lang="ts" setup>
import { ref, onMounted, toRaw, } from 'vue';
import { actionsFormSchema } from './actions';
import { useVbenForm, useVbenModal } from '@vben/common-ui';
import { List as ListProduct } from '#/api/device/iotProduct';
import SelectDevice from '../selectDevice/index.vue';
import { Input, } from 'ant-design-vue';
import PropertyConfig from './PropertyConfig.vue';
import ActionConfig from './ActionConfig.vue';
import { cloneDeep } from '@vben/utils';
import CustomDataConfig from './CustomDataConfig.vue';
import { nextTick } from 'vue';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import ModbusRtuConfig from './ModbusRtuConfig.vue';

interface ActionFormData {
  id?: number;
  source?: number; // 来源，0:规则引擎，1:设备事件
  deviceKeys?: string;
  deviceNames?: string;
  productKey?: string;
  productName?: string;
  downDataType?: string; // 下发数据类型，0:属性，1:事件
  params?: any[];
  [key: string]: any;
  modelTypeKey?: string; // 动作类型key
  topicType?: string;        // 数据流类型
  customTopic?: string;      // 自定义数据流
  dataType?: string;     // 消息格式
  dataMsg?: string;          // 下发消息
  useSubAddr?: number; // 是否使用子设备地址
  subAddr?: number; // 从机地址
  modbusFunction?: string; // 功能码
  regAddr?: string; // 起始寄存器地址
  coilCount?: number; // 线圈数量
  regCount?: number; // 寄存器数量
  coilValue?: string; // 线圈值
  regValue?: string; // 寄存器值
  coilStatusList?: []; // 线圈状态列表
  regValueList?: number[]; // 寄存器值列表
  dataCheckType?: string; // 数据校验类型
}

const formData = ref<ActionFormData>({
  id: 0,
  source: 2,
  deviceKeys: '',
  deviceNames: '',
  productKey: '',
  productName: '',
  downDataType: '1', // 默认下发属性
  params: [],
  modelTypeKey: '', // 动作类型key
  //加入自定义数据的
  topicType: '',        // 数据流类型
  customTopic: '',      // 自定义数据流
  dataType: 'json',     // 消息格式
  dataMsg: '',          // 下发消息  和Modbus RTU指令的dataMsg是一样的
  //加入下发Modbus RTU指令
  useSubAddr: 0,
  subAddr: 1,
  modbusFunction: '',
  regAddr: '',
  coilCount: 0,
  regCount: 0,
  coilValue: '',
  regValue: '',
  coilStatusList: [],
  regValueList: [],
  dataCheckType: '',

});
const productOptions = ref<{ label: string; value: string }[]>([]);

// 加载产品列表
async function loadProductOptions() {
  const res = await ListProduct({ page: 1, pageSize: 1000 });
  productOptions.value = (res.items || []).map((item: any) => ({
    label: item.productName,
    value: item.productKey,
  }));
  // 动态更新表单schema的options
  actionFormApi.updateSchema([
    {
      fieldName: 'productKey',
      componentProps: {
        options: productOptions.value,
        showSearch: true,      // 开启搜索
        allowClear: true,      // 可清空（可选）
        placeholder: '请选择触发产品', // 可选
        filterOption: (input: string, option: any) => {
          // 支持输入内容与label模糊匹配
          return option.label?.toLowerCase().includes(input.toLowerCase());
        },
        onChange: (val: string, option: any) => {
          console.log('产品下拉选中:', val);
          console.log('产品下拉选中label:', option.label);
          // 手动同步到 formData，确保响应式
          formData.value.productName = option.label;
          formData.value.productKey = val;
          actionFormApi.setFieldValue('productKey', val);
          actionFormApi.setFieldValue('productName', option.label);
          actionFormApi.setFieldValue('deviceKeys', ''); // 清空参数
          actionFormApi.setFieldValue('deviceNames', '');
        },
      },
    },
  ]);
}

// 初始化时加载
onMounted(() => {
  loadProductOptions();
});
// 打开设备弹窗，点击事件
async function openDeviceModal() {
  const actionsformData = cloneDeep(await actionFormApi.getValues());
  console.log('打开设备选择弹窗，当前动作表单数据:', actionsformData);
  modalSelectDeviceApi.setData({
    productKey: actionsformData.productKey,
  });
  console.log('添加动作里面的productKey', actionsformData.productKey);
  modalSelectDeviceApi.open();
}

// 设备选择回调
function onDeviceSelected(keys: string, names: string) {
  // keys, names 都是逗号分隔字符串
  formData.value.deviceKeys = keys;
  formData.value.deviceNames = names;
  actionFormApi.setFieldValue('deviceKeys', keys);
  actionFormApi.setFieldValue('deviceNames', names);
}

const [ActionForm, actionFormApi] = useVbenForm({
  schema: [...actionsFormSchema,
  {
    fieldName: 'downDataType',
    label: '动作类型',
    component: 'Select',
    formItemClass: 'col-span-2', // 独占一行
    componentProps: {
      options: getDictOptions(DictEnum.DOWN_DATA_TYPE), // 1=下发属性，2=下发动作，3=下发自定义数据，4=下发Modbus RTU指令
      placeholder: '请选择动作类型',
      style: { width: '320px' }, // 设置宽度
      onChange: (value: any, form: any) => {
        formData.value.downDataType = value; // 更新全局变量
      }
    },
  },],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 gap-x-4',
  handleValuesChange: (values: any) => {
    // 监听表单值变化
    if (values.productKey) {
      formData.value.productKey = values.productKey;
    }
  },
});

const emit = defineEmits(['save']);

function handleConfirm() {
  actionFormApi.validate().then((res: any) => {
    if (res.valid) {

      actionFormApi.getValues().then((data: any) => {
        // 把PropertyConfigData ......中的属性放入data
        //有两个params，一个是PropertyConfigData.params，一个是ActionConfigData.params，需要做个判断，加入到data中
        if (data.downDataType === '1') {
          // 下发属性
          data.params = toRaw(PropertyConfigData.value.params || []);
        } else if (data.downDataType === '2') {
          // 下发动作
          data.params = toRaw(ActionConfigData.value.params || []);
          data.modelTypeKey = ActionConfigData.value.modelTypeKey || '';
        }
        else if (data.downDataType === '3') {
          // 自定义数据
          data.customTopic = toRaw(CustomDataConfigData.value.customTopic || []);
          data.topicType = toRaw(CustomDataConfigData.value.topicType || '');
          data.dataType = toRaw(CustomDataConfigData.value.dataType || 'json');
          data.dataMsg = toRaw(CustomDataConfigData.value.dataMsg || []);
        }
        else if (data.downDataType === '4') {
          // 下发Modbus RTU指令
          data.topicType = toRaw(ModbusRtuConfigData.value.topicType || '');
          data.customTopic = toRaw(ModbusRtuConfigData.value.customTopic || []);
          data.useSubAddr = toRaw(ModbusRtuConfigData.value.useSubAddr || 0);
          data.subAddr = toRaw(ModbusRtuConfigData.value.subAddr || 1);
          data.modbusFunction = toRaw(ModbusRtuConfigData.value.modbusFunction || '');
          data.regAddr = toRaw(ModbusRtuConfigData.value.regAddr || '');
          data.coilCount = toRaw(ModbusRtuConfigData.value.coilCount || 0);
          data.regCount = toRaw(ModbusRtuConfigData.value.regCount || 0);
          data.coilValue = toRaw(ModbusRtuConfigData.value.coilValue || '');
          data.regValue = toRaw(ModbusRtuConfigData.value.regValue || '');
          data.coilStatusList = toRaw(ModbusRtuConfigData.value.coilStatusList || []);
          data.regValueList = toRaw(ModbusRtuConfigData.value.regValueList || []);
          data.dataCheckType = toRaw(ModbusRtuConfigData.value.dataCheckType || '');
          data.dataMsg = toRaw(ModbusRtuConfigData.value.dataMsg || ''); // Modbus RTU指令的消息内容
        }

        console.log('弹窗表单数据：', data); // 打印表单数据
        emit('save', data);
        modalApi.close();
      });
    }
  });
}

const [Modal, modalApi] = useVbenModal({
  onConfirm: handleConfirm,
  onCancel: () => {
    modalApi.close();
  },
  async onOpenChange(isOpen) {
    if (isOpen) {
      const currentData = modalApi.getData();
      console.log('打开模态框时传入的值:', currentData);
      formData.value = {
        id: currentData.id || 0,
        source: currentData.source || '2',
        deviceKeys: currentData.deviceKeys || '',
        deviceNames: currentData.deviceNames || '',
        productKey: currentData.productKey || '',
        productName: currentData.productName || '',
        downDataType: currentData.downDataType || '1', // 默认下发属性
        params: currentData.params || [],
        modelTypeKey: currentData.modelTypeKey || '', // 动作类型key

        //存储上关于自定义数据的
        customTopic: currentData.customTopic || [],  // 自定义数据流
        topicType: currentData.topicType || '',
        dataType: currentData.dataType || 'json', // 消息格式
        dataMsg: currentData.dataMsg || '', // 下发消息

        //存储下发Modbus RTU指令的
        useSubAddr: currentData.useSubAddr || 0, // 是否使用子设备地址
        subAddr: currentData.subAddr || 1, // 从机地址
        modbusFunction: currentData.modbusFunction || '', // 功能码
        regAddr: currentData.regAddr || '', // 起始寄存器地址
        coilCount: currentData.coilCount || 0, // 线圈数量
        regCount: currentData.regCount || 0, // 寄存器数量
        coilValue: currentData.coilValue || '', // 线圈值
        regValue: currentData.regValue || '', // 寄存器值
        coilStatusList: currentData.coilStatusList || [], // 线圈状态列表
        regValueList: currentData.regValueList || [], // 寄存器值列表
        dataCheckType: currentData.dataCheckType || '', // 数据校验类型

      };
      formData.value.topicType = currentData.topicType !== undefined && currentData.topicType !== null
        ? String(currentData.topicType) : '';
      ModbusRtuConfigData.value.topicType = currentData.topicType !== undefined && currentData.topicType !== null
        ? String(currentData.topicType)
        : '';

      //给下发动作--方法清空，在新增的时候
      ActionConfigData.value = {
        modelTypeKey: currentData.modelTypeKey || '',
        params: currentData.params || [],
        functionOptions: currentData.functionOptions || [],
      };
      //给自定义数据也清空
      CustomDataConfigData.value = {
        topicType: currentData.topicType || '',
        customTopic: currentData.customTopic || '',
        dataType: currentData.dataType || 'json',
        dataMsg: currentData.dataMsg || '',
      };
      //给下发Modbus RTU指令的也清空
      ModbusRtuConfigData.value = {
        topicType: currentData.topicType || '',
        customTopic: currentData.customTopic || '',
        useSubAddr: currentData.useSubAddr || 0,
        subAddr: currentData.subAddr || 1,
        modbusFunction: currentData.modbusFunction || '',
        regAddr: currentData.regAddr || '',
        coilCount: currentData.coilCount || 0,
        regCount: currentData.regCount || 0,
        coilValue: currentData.coilValue || '',
        regValue: currentData.regValue || '',
        coilStatusList: currentData.coilStatusList || [],
        regValueList: currentData.regValueList || [],
        dataCheckType: currentData.dataCheckType || '',
        dataMsg: currentData.dataMsg || '', // Modbus RTU指令的消息内容
      };


      // TODO 判断并设置PropertyConfigData 。。。。。的值
      // 打开时加载产品列表，共同加载的；
      await loadProductOptions();
      await actionFormApi.setValues({ ...formData.value });
      //每个组件都需要在打开时加载自己的数据，然后做判断
      if (formData.value.downDataType === '1') {
        peropConfig.value?.setParamsOld(toRaw(formData.value.params) || []);

      } else if (formData.value.downDataType === '2') {
        actionConfig.value?.setInputparams(toRaw(formData.value.params) || []);

        ActionConfigData.value.modelTypeKey = formData.value.modelTypeKey || '';
      }
      else if (formData.value.downDataType === '3') {
        CustomDataConfigData.value.topicType = formData.value.topicType || '';
        CustomDataConfigData.value.customTopic = formData.value.customTopic || '';
        CustomDataConfigData.value.dataType = formData.value.dataType || 'json';
        CustomDataConfigData.value.dataMsg = formData.value.dataMsg || '';
      }
      else if (formData.value.downDataType === '4') {
        ModbusRtuConfigData.value.topicType = formData.value.topicType || '';
        ModbusRtuConfigData.value.customTopic = formData.value.customTopic || '';
        ModbusRtuConfigData.value.useSubAddr = formData.value.useSubAddr || 0;
        ModbusRtuConfigData.value.subAddr = formData.value.subAddr || 1;
        ModbusRtuConfigData.value.modbusFunction = formData.value.modbusFunction || '';
        ModbusRtuConfigData.value.regAddr = formData.value.regAddr || '';
        ModbusRtuConfigData.value.coilCount = formData.value.coilCount || 0;
        ModbusRtuConfigData.value.regCount = formData.value.regCount || 0;
        ModbusRtuConfigData.value.coilValue = formData.value.coilValue || '';
        ModbusRtuConfigData.value.regValue = formData.value.regValue || '';
        ModbusRtuConfigData.value.coilStatusList = formData.value.coilStatusList || [];
        ModbusRtuConfigData.value.regValueList = formData.value.regValueList || [];
        ModbusRtuConfigData.value.dataCheckType = formData.value.dataCheckType || '';
        ModbusRtuConfigData.value.dataMsg = formData.value.dataMsg || '';   // Modbus RTU指令的消息内容

      }

      // 打印打开模态框时设置的表单数据
      console.log('打开模态框时设置的表单数据:', CustomDataConfigData.value, await actionFormApi.getValues());
      nextTick(() => {
        console.log("dom 刷新")
      });
    }
  },
});

const peropConfig = ref<InstanceType<typeof PropertyConfig>>();
const actionConfig = ref<InstanceType<typeof ActionConfig>>();
const customDataConfig = ref<InstanceType<typeof CustomDataConfig>>();
const modbusRtuConfig = ref<InstanceType<typeof ModbusRtuConfig>>();
const [SelectDeviceModel, modalSelectDeviceApi] = useVbenModal({
  zIndex: 2002,
  // 连接抽离的组件
  connectedComponent: SelectDevice,
});
//下发属性
const PropertyConfigData = ref({
  params: []
});
//下发动作
const ActionConfigData = ref({
  modelTypeKey: '',
  params: [],
  functionOptions: [],
});
//下发自定义数据的；
const CustomDataConfigData = ref({
  topicType: '',        // 数据流类型
  customTopic: '',      // 自定义数据流
  dataType: 'json',     // 消息格式
  dataMsg: '',          // 下发消息
});
// 关于下发Modbus RTU指令的；
const ModbusRtuConfigData = ref<{
  topicType: string;
  customTopic: string;
  useSubAddr: number;
  subAddr: number;
  modbusFunction: string;
  regAddr: string;
  coilCount: number;
  regCount: number;
  coilValue: string;
  regValue: string;
  coilStatusList: [];
  regValueList: number[];
  dataCheckType: string;
  dataMsg: string; //Modbus RTU指令的消息内容
}>({
  topicType: '',   // 数据流类型
  customTopic: '', // 自定义数据流
  useSubAddr: 0,   // 是否使用子设备地址
  subAddr: 1,  // 从机地址
  modbusFunction: '',  // 功能码
  regAddr: '',    //起始寄存器地址
  coilCount: 0,   // 线圈数量
  regCount: 0,    // 寄存器数量
  coilValue: '',  // 线圈值
  regValue: '',   // 寄存器值
  coilStatusList: [],   // 线圈状态列表
  regValueList: [],    // 寄存器值列表
  dataCheckType: '',    // 数据校验类型
  dataMsg: '', //Modbus RTU指令的消息内容
});
</script>
<template>

  <Modal class="h-[600px] w-[1000px]" title="编辑执行动作">
    <ActionForm>
      <!-- 设备选择插槽 -->
      <template #deviceNames="slotProps">
        <Input placeholder="请选择设备" v-bind="slotProps">
        <template #addonAfter>
          <span @click="openDeviceModal"> 选择 </span>
        </template>
        </Input>
      </template>
    </ActionForm>
    <!-- 使用downDataType来区分下发属性和下发事件所对应的标签页面 -->
    <div>
      <!-- 关于下发属性的； -->
      <PropertyConfig v-show="formData.downDataType == '1'" v-model:params="PropertyConfigData.params" ref="peropConfig"
        :productKey="formData.productKey || ''">
      </PropertyConfig>
      <!-- <pre>当前参数：{{ PropertyConfigData.params }}</pre> -->

      <!-- 关于下发动作的； -->
      <ActionConfig v-show="formData.downDataType == '2'" v-model:params="ActionConfigData.params" ref="actionConfig"
        v-model:modelTypeKey="ActionConfigData.modelTypeKey" :productKey="formData.productKey || ''">
      </ActionConfig>

      <!-- 关于自定义数据 -->
      <CustomDataConfig v-show="formData.downDataType == '3'" v-model:customTopic="CustomDataConfigData.customTopic"
        v-model:topicType="CustomDataConfigData.topicType" v-model:dataType="CustomDataConfigData.dataType"
        v-model:dataMsg="CustomDataConfigData.dataMsg" ref="customDataConfig" />

      <!-- 关于下发Modbus RTU指令的； -->
      <ModbusRtuConfig v-show="formData.downDataType == '4'" v-model:topicType="ModbusRtuConfigData.topicType"
        v-model:customTopic="ModbusRtuConfigData.customTopic" v-model:useSubAddr="ModbusRtuConfigData.useSubAddr"
        v-model:subAddr="ModbusRtuConfigData.subAddr" v-model:modbusFunction="ModbusRtuConfigData.modbusFunction"
        v-model:regAddr="ModbusRtuConfigData.regAddr" v-model:coilCount="ModbusRtuConfigData.coilCount"
        v-model:regCount="ModbusRtuConfigData.regCount" v-model:coilValue="ModbusRtuConfigData.coilValue"
        v-model:regValue="ModbusRtuConfigData.regValue" v-model:coilStatusList="ModbusRtuConfigData.coilStatusList"
        v-model:regValueList="ModbusRtuConfigData.regValueList"
        v-model:dataCheckType="ModbusRtuConfigData.dataCheckType" v-model:dataMsg="ModbusRtuConfigData.dataMsg"
        ref="modbusRtuConfig " />
    </div>

    <!-- 设备弹窗 -->
    <SelectDeviceModel @deviceSelected="onDeviceSelected" />
  </Modal>
</template>
<style scoped>
.action-debug-info {
  margin-top: 16px;
  /* 与上方按钮的距离 */
  display: block;
  line-height: 1.6;
  white-space: pre-wrap;
  /* 让长数据自动换行 */
}
</style>
