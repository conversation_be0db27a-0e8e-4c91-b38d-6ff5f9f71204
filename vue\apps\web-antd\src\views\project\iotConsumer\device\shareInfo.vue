<script setup lang="ts">
import type { SysUserResetPasswordParam, SysUserListData } from '#/api/system/user';

import { useVbenModal, z } from '@vben/common-ui';
import { message } from 'ant-design-vue';
import { useVbenForm } from '#/adapter/form';
import { resetSysUserPassword } from '#/api/system/user';
import { Description, useDescription } from '#/components/description';
import { useVbenVxeGrid, type VxeTableGridOptions } from '@vben/plugins/vxe-table';
import { shareInfoColumns, type RowType } from './model';
import { ref } from 'vue';

const title = ref('XXX设备共享');
const gridOptions: VxeTableGridOptions<RowType> = {
    checkboxConfig: {
        highlight: true,
    },
    rowConfig: {
        keyField: 'deviceConsumerId',
    },
    columns: shareInfoColumns,
    exportConfig: {},
    height: '300px',
    keepSource: true,
    showOverflow: false,
    pagerConfig: {},
    proxyConfig: {
        // ajax: {
        //     query: async ({ page }, formValues) => {
        //         const response = await View({
        //             id: props.consumer.consumerId
        //         });
        //         return response;
        //     },
        // },
    },
    toolbarConfig: {
        custom: false,
        export: false,
        refresh: false,
        resizable: true,
        search: false,
        zoom: false,
    },
};
const [Grid, gridApi] = useVbenVxeGrid({
    gridOptions,
});

const [BasicModal, modalApi] = useVbenModal({
    onCancel: handleCancel,
    onOpenChange: (open) => {
        if (open) {
            const { record } = modalApi.getData();
            // 动态更新标题为设备名称（假设设备名称字段为 deviceName）
            title.value = `${record.device.deviceName || '未知设备'}共享信息`;
        }
    },
});

async function handleCancel() {
    modalApi.close();
}
</script>

<template>
    <BasicModal :close-on-click-modal="false" :fullscreen-button="false" :title="title">
        <Grid>
        </Grid>
    </BasicModal>
</template>
