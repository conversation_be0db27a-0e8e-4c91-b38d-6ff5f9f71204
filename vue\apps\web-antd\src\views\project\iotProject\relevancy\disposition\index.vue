<template>
  <Modal
    v-model:open="visible"
    :width="'100vw'"
    :style="{ top: 0, paddingBottom: 0 }"
    :bodyStyle="{ height: 'calc(100vh - 55px)', padding: '24px', overflow: 'auto' }"
    :mask-closable="false"
    :closable="false"
    :footer="null"
    class="disposition-modal"
    wrapClassName="fullscreen-modal"
  >
    <!-- 自定义标题栏 -->
    <template #title>
      <div class="custom-title-bar">
        <span class="title-text">{{ title }}</span>
        <div class="title-actions">
          <Button type="text" size="small" @click="handleRefresh" title="刷新">
            <IconifyIcon icon="ant-design:reload-outlined" />
          </Button>
          <Button type="primary" size="small" @click="handleConfirm">
            确定
          </Button>
          <Button size="small" @click="handleCancel">
            取消
          </Button>
          <Button type="text" size="small" @click="handleCancel" class="close-btn">
            <IconifyIcon icon="ant-design:close-outlined" />
          </Button>
        </div>
      </div>
    </template>

    <!-- 面板配置内容区域 -->
    <div class="panel-container">
      <!-- 左侧控制台 -->
      <div class="control-panel left-panel">
        <div class="panel-title">控制台1</div>
        <div class="panel-content">
          <!-- 控制台1的内容 -->
        </div>
      </div>

      <!-- 中间被控区域 -->
      <div
        class="controlled-area"
        @wheel="handleWheel"
        ref="controlledAreaRef"
      >
        <div
          class="controlled-content"
          ref="controlledContentRef"
          :style="{
            // 添加垂直偏移样式
            transform: `translateY(${verticalOffset}px)`
          }"
        >
         
          <!-- 新增左右卡片 -->
          <div class="card-container">
            <div class="card left-card"></div>
            
            <div class="card right-card"></div>
          </div>
          <!-- 被控台的内容 -->
        </div>
      </div>

      <!-- 右侧控制台 -->
      <div class="control-panel right-panel">
        <div class="panel-title">控制台2</div>
        <div class="panel-content">
          <!-- 控制台2的内容 -->
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Modal, Button } from 'ant-design-vue';
import { IconifyIcon } from '@vben/icons';

interface ModalProps {
  ppId?: number;
  productKey?: string;
  productAlias?: string;
}

const visible = ref(false);
const modalData = ref<ModalProps>({});
// 新增垂直偏移量
const verticalOffset = ref(0);
const controlledAreaRef = ref<HTMLElement | null>(null);
const controlledContentRef = ref<HTMLElement | null>(null);
let maxOffset = 0;

const title = computed(() => {
  const { productAlias, productKey } = modalData.value;
  const displayName = productAlias || productKey;
  return displayName ? `面板配置 - ${displayName}` : '面板配置';
});

function open(data: ModalProps) {
  modalData.value = data;
  visible.value = true;
  console.log('打开面板配置，产品:', data.productAlias || data.productKey);
}

function close() {
  visible.value = false;
  modalData.value = {};
}

function handleRefresh() {
  console.log('刷新面板配置');
  // 重置垂直偏移
  verticalOffset.value = 0;
  console.log('已重置垂直偏移');
}

function handleConfirm() {
  console.log('确认面板配置');
  close();
}

function handleCancel() {
  console.log('取消面板配置');
  close();
}

// 计算最大偏移量
function calculateMaxOffset() {
  if (controlledAreaRef.value && controlledContentRef.value) {
    const containerHeight = controlledAreaRef.value.clientHeight;
    const contentHeight = controlledContentRef.value.clientHeight;
    maxOffset = Math.max(contentHeight - containerHeight, 0);
  }
}

// 处理滚轮上下滚动移动
function handleWheel(event: WheelEvent) {
  event.preventDefault();

  const delta = event.deltaY;
  const moveStep = 20;

  // 计算新的偏移量
  let newOffset = verticalOffset.value;

  // 向上滚动，内容向上移动
  if (delta < 0) {
    newOffset = Math.max(verticalOffset.value - moveStep, 0);
  } else {
    // 向下滚动，内容向下移动
    newOffset = Math.min(verticalOffset.value + moveStep, maxOffset);
  }

  verticalOffset.value = newOffset;
  console.log('垂直偏移量:', verticalOffset.value);
}

onMounted(() => {
  calculateMaxOffset();
});

// 暴露方法给父组件
defineExpose({
  open,
  close
});
</script>

<style scoped>
/* 自定义标题栏样式 */
.custom-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.title-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.close-btn:hover {
  color: rgba(0, 0, 0, 0.75);
}

/* 面板配置布局样式 */
.panel-container {
  display: flex;
  height: 100%;
  background-color: #f5f5f5;
}

/* 左右控制台样式 */
.control-panel {
  width: 250px;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.left-panel {
  border-right: none;
}

.right-panel {
  border-left: none;
}

.panel-title {
  padding: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #ff4d4f;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 中间被控区域样式 */
.controlled-area {
  flex: 1;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  user-select: none; /* 防止拖拽时选中文字 */
}

.controlled-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  transition: transform 0.2s ease;
  transform-origin: center center;
}

.controlled-title {
  font-size: 16px;
  color: #ff4d4f;
  font-weight: 500;
}

/* 全屏模态框样式 */
:deep(.fullscreen-modal) {
  .ant-modal {
    max-width: 100vw !important;
    width: 100vw !important;
    height: 100vh !important;
    margin: 0 !important;
    top: 0 !important;
    padding-bottom: 0 !important;
  }

  .ant-modal-content {
    height: 100vh !important;
    border-radius: 0 !important;
  }

  .ant-modal-header {
    border-radius: 0 !important;
    padding: 16px 24px !important;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-modal-body {
    height: calc(100vh - 55px) !important;
    padding: 24px !important;
    overflow: auto !important;
  }

  /* 隐藏底部按钮区域 */
  .ant-modal-footer {
    display: none !important;
  }

  /* 隐藏默认关闭按钮 */
  .ant-modal-close {
    display: none !important;
  }
}

/* 卡片容器样式 */
.card-container {
  display: flex;
  justify-content: space-between;
  width: 80%; /* 容器宽度占被控区域宽度的 80% */
  margin-top: 20px;
}

/* 卡片通用样式 */
.card {
  background-color: white;
  aspect-ratio: 1 / 2; /* 长比高为 1:2 */
  width: 40%; /* 卡片宽度占容器宽度的 20% */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 添加阴影 */
  border-radius: 8px; /* 圆角 */
}

/* 左卡片样式 */
.left-card {
  /* 可添加左卡片特有的样式 */
}

/* 右卡片样式 */
.right-card {
  /* 可添加右卡片特有的样式 */
}
</style>