<template>
  <Modal
    v-model:open="visible"
    :width="'100vw'"
    :style="{ top: 0, paddingBottom: 0 }"
    :bodyStyle="{ height: 'calc(100vh - 55px)', padding: '24px', overflow: 'auto' }"
    :mask-closable="false"
    :closable="false"
    :footer="null"
    class="disposition-modal"
    wrapClassName="fullscreen-modal"
  >
    <!-- 自定义标题栏 -->
    <template #title>
      <div class="custom-title-bar">
        <span class="title-text">{{ title }}</span>
        <div class="title-actions">
          <Button type="text" size="small" @click="handleRefresh" title="刷新">
            <IconifyIcon icon="ant-design:reload-outlined" />
          </Button>
          <Button type="primary" size="small" @click="handleConfirm">
            确定
          </Button>
          <Button size="small" @click="handleCancel">
            取消
          </Button>
          <Button type="text" size="small" @click="handleCancel" class="close-btn">
            <IconifyIcon icon="ant-design:close-outlined" />
          </Button>
        </div>
      </div>
    </template>

    <!-- 面板配置内容区域 -->
    <div class="panel-container">
      <!-- 左侧控制台 -->
      <div class="control-panel left-panel">
        <div class="panel-title">控制台1</div>
        <div class="panel-content">
          <!-- 控制台1的内容 -->
        </div>
      </div>

      <!-- 中间被控区域 -->
      <div
        :class="['controlled-area', { dragging: isDragging }]"
        @wheel="handleWheel"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseUp"
        ref="controlledAreaRef"
      >
        <div
          class="controlled-content"
          :style="{
            transform: `translate(${translateX}px, ${translateY + scrollY}px)`
          }"
        >
         
       

          <!-- 中间的两个卡片 -->
          <div class="card-container">
            <Card
              class="left-card"
              title="左侧卡片"
              :bordered="true"
              :bodyStyle="{ padding: '16px', minHeight: '200px' }"
            >
              <p>左侧卡片内容</p>
            </Card>

            <Card
              class="right-card"
              title="右侧卡片"
              :bordered="true"
              :bodyStyle="{ padding: '16px', minHeight: '200px' }"
            >
              <p>右侧卡片内容</p>
            </Card>
          </div>
          <!-- 被控台的内容 -->
        </div>
      </div>

      <!-- 右侧控制台 -->
      <div class="control-panel right-panel">
        <div class="panel-title">控制台2</div>
        <div class="panel-content">
          <!-- 控制台2的内容 -->
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Modal, Button, Card } from 'ant-design-vue';
import { IconifyIcon } from '@vben/icons';

interface ModalProps {
  ppId?: number;
  productKey?: string;
  productAlias?: string;
}

const visible = ref(false);
const modalData = ref<ModalProps>({});
const controlledAreaRef = ref();

// 拖拽相关状态
const translateX = ref(0);
const translateY = ref(0);
const isDragging = ref(false);
const lastMouseX = ref(0);
const lastMouseY = ref(0);

// 滚动相关状态
const scrollY = ref(0);

const title = computed(() => {
  const { productAlias, productKey } = modalData.value;
  const displayName = productAlias || productKey;
  return displayName ? `面板配置 - ${displayName}` : '面板配置';
});

function open(data: ModalProps) {
  modalData.value = data;
  visible.value = true;
  console.log('打开面板配置，产品:', data.productAlias || data.productKey);
}

function close() {
  visible.value = false;
  modalData.value = {};
}

function handleRefresh() {
  console.log('刷新面板配置');
  // 重置位置和滚动
  translateX.value = 0;
  translateY.value = 0;
  scrollY.value = 0;
  isDragging.value = false;
  console.log('已重置位置和滚动');
}

function handleConfirm() {
  console.log('确认面板配置');
  close();
}

function handleCancel() {
  console.log('取消面板配置');
  close();
}

// 处理滚轮上下滚动
function handleWheel(event: WheelEvent) {
  event.preventDefault();

  const delta = event.deltaY;
  const scrollStep = 50; // 每次滚动的像素距离

  if (delta < 0) {
    // 向上滚动，内容向上移动
    scrollY.value -= scrollStep;
  } else {
    // 向下滚动，内容向下移动
    scrollY.value += scrollStep;
  }

  console.log('滚动位置:', scrollY.value);
}

// 处理鼠标按下开始拖拽
function handleMouseDown(event: MouseEvent) {
  isDragging.value = true;
  lastMouseX.value = event.clientX;
  lastMouseY.value = event.clientY;
  console.log('开始拖拽');
}

// 处理鼠标移动拖拽
function handleMouseMove(event: MouseEvent) {
  if (!isDragging.value) return;

  const deltaX = event.clientX - lastMouseX.value;
  const deltaY = event.clientY - lastMouseY.value;

  translateX.value += deltaX;
  translateY.value += deltaY;

  lastMouseX.value = event.clientX;
  lastMouseY.value = event.clientY;

  console.log('拖拽位置:', translateX.value, translateY.value);
}

// 处理鼠标松开结束拖拽
function handleMouseUp() {
  if (isDragging.value) {
    isDragging.value = false;
    console.log('结束拖拽');
  }
}

// 暴露方法给父组件
defineExpose({
  open,
  close
});
</script>

<style scoped>
/* 自定义标题栏样式 */
.custom-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.title-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.close-btn:hover {
  color: rgba(0, 0, 0, 0.75);
}

/* 面板配置布局样式 */
.panel-container {
  display: flex;
  height: 100%;
  background-color: #f5f5f5;
}

/* 左右控制台样式 */
.control-panel {
  width: 250px;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.left-panel {
  border-right: none;
}

.right-panel {
  border-left: none;
}

.panel-title {
  padding: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #ff4d4f;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fafafa;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* 中间被控区域样式 */
.controlled-area {
  flex: 1;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  user-select: none; /* 防止拖拽时选中文字 */
}

.controlled-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; /* 改为顶部对齐，避免内容被遮挡 */
  width: 100%;
  min-height: 100%; /* 改为最小高度，允许内容超出 */
  padding-top: 60px; /* 添加顶部内边距 */
  transition: transform 0.2s ease;
  transform-origin: center center;
}

.controlled-title {
  font-size: 16px;
  color: #ff4d4f;
  font-weight: 500;
  margin-bottom: 20px;
}

/* 卡片容器样式 */
.card-container {
  display: flex;
  justify-content: space-between;
  width: 80%;
  gap: 20px;
  margin-top: 40px; /* 增加顶部间距，确保卡片标题完全显示 */
}

/* 卡片样式 */
.left-card,
.right-card {
  width: 45%;
  aspect-ratio: 1 / 2; /* 长比高为 1:2 */
}

/* Card组件内部样式调整 */
.left-card :deep(.ant-card-head),
.right-card :deep(.ant-card-head) {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.left-card :deep(.ant-card-head-title),
.right-card :deep(.ant-card-head-title) {
  font-size: 14px;
  font-weight: 500;
}

.left-card :deep(.ant-card-body),
.right-card :deep(.ant-card-body) {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

/* 全屏模态框样式 */
:deep(.fullscreen-modal) {
  .ant-modal {
    max-width: 100vw !important;
    width: 100vw !important;
    height: 100vh !important;
    margin: 0 !important;
    top: 0 !important;
    padding-bottom: 0 !important;
  }

  .ant-modal-content {
    height: 100vh !important;
    border-radius: 0 !important;
  }

  .ant-modal-header {
    border-radius: 0 !important;
    padding: 16px 24px !important;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-modal-body {
    height: calc(100vh - 55px) !important;
    padding: 24px !important;
    overflow: auto !important;
  }

  /* 隐藏底部按钮区域 */
  .ant-modal-footer {
    display: none !important;
  }

  /* 隐藏默认关闭按钮 */
  .ant-modal-close {
    display: none !important;
  }
}


</style>