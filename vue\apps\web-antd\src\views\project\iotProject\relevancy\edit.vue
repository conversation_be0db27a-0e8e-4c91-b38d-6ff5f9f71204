<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[600px]">
    <div class="p-4">
      <!-- 标题 -->
    

      <!-- 全局选项 -->
      <div class="mb-8">
        <h4 class="text-base font-medium mb-2">全局选项</h4>
        <p class="text-gray-500 text-sm mb-4">对于普通设备用户的设备产品，可设置设备或设备第二维码等一些其他，最终用户可在 App 中主动添加设备。</p>

        <!-- 允许用户添加设备 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium mb-2">允许用户添加设备</h5>
          <p class="text-gray-500 text-sm mb-3">允许用户在 App 应用中添加该类设备。</p>
          <Switch v-model:checked="formData.allowUserAdd" />
        </div>

        <!-- 允许多用户添加 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium mb-2">允许多用户添加</h5>
          <p class="text-gray-500 text-sm mb-3">是否允许多个用户添加同一个设备。</p>
          <Switch v-model:checked="formData.allowMultiUserAdd" />
        </div>
      </div>

      <!-- 设备码选项 -->
      <div class="mb-8">
        <h4 class="text-base font-medium mb-4">设备码选项</h4>
        <p class="text-gray-500 text-sm mb-4">用户可通过设备码添加设备，请在设备信息中设置设备码。</p>

        <!-- 自定义显示文字 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium mb-2">自定义显示文字</h5>
          <p class="text-gray-500 text-sm mb-3">显示在用户输入设备码的页面，未设置则显示系统默认文字。</p>
          <Input v-model:value="formData.customDisplayText" placeholder="请输入自定义显示文字" />
        </div>

        <!-- 显示提示图片 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium mb-2">显示提示图片</h5>
          <p class="text-gray-500 text-sm mb-3">开启后，在用户输入设备码的页面显示示图片。</p>
          <Switch v-model:checked="formData.showHintImage" />
        </div>

        <!-- 上传提示图片 -->
        <div class="mb-6">
          <h5 class="text-sm font-medium mb-2">上传提示图片</h5>
          <div class="mb-3">
            <Button type="primary" @click="handleUploadClick">选择图片</Button>
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              style="display: none"
              @change="handleFileChange"
            />
          </div>
          <p class="text-gray-500 text-sm">
            支持的图片格式：PNG、图片文件大小不超过 256KB。
          
          </p>

          <!-- 图片预览 -->
          <div v-if="formData.hintImageUrl" class="mt-4">
            <img :src="formData.hintImageUrl" alt="提示图片" class="max-w-xs max-h-32 border rounded" />
            <div class="mt-2">
              <Button size="small" @click="handleRemoveImage">删除图片</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { Switch, Input, Button, message } from 'ant-design-vue';

import { useVbenDrawer } from '@vben/common-ui';

const emit = defineEmits<{ reload: [] }>();

interface ModalProps {
  productId?: number | string;
  productName?: string;
  update: boolean;
  view: boolean;
}

// 表单数据
const formData = ref({
  allowUserAdd: false,           // 允许用户添加设备
  allowMultiUserAdd: false,      // 允许多用户添加
  customDisplayText: '',         // 自定义显示文字
  showHintImage: false,          // 显示提示图片
  hintImageUrl: '',              // 提示图片URL
});

const isUpdate = ref(false);
const isView = ref(false);
const fileInput = ref<HTMLInputElement>();

const title = computed(() => {
  if (isView.value) {
    return '查看配置';
  }
  return isUpdate.value ? '编辑配置' : '配置用户添加设备';
});

// 文件上传处理
function handleUploadClick() {
  fileInput.value?.click();
}

async function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (!file) return;

  // 检查文件大小 (256KB = 256 * 1024 bytes)
  if (file.size > 256 * 1024) {
    message.error('图片文件大小不能超过 256KB');
    return;
  }

  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    message.error('请选择图片文件');
    return;
  }

  try {
    // 这里应该调用上传API
    // const response = await uploadApi(file);
    // formData.value.hintImageUrl = response.url;

    // 临时使用本地预览
    const reader = new FileReader();
    reader.onload = (e) => {
      formData.value.hintImageUrl = e.target?.result as string;
    };
    reader.readAsDataURL(file);

    message.success('图片上传成功');
  } catch (error) {
    message.error('图片上传失败');
  }
}

function handleRemoveImage() {
  formData.value.hintImageUrl = '';
  if (fileInput.value) {
    fileInput.value.value = '';
  }
}

const [BasicDrawer, drawerApi] = useVbenDrawer({
  onCancel: handleCancel,
  onConfirm: handleConfirm,
  async onOpenChange(isOpen) {
    if (!isOpen) {
      return null;
    }

    drawerApi.setState({ confirmLoading: true, loading: true });

    const { productId, productName, update, view } = drawerApi.getData() as ModalProps;
    isUpdate.value = update;
    isView.value = view;

    // 重置表单数据
    formData.value = {
      allowUserAdd: false,
      allowMultiUserAdd: false,
      customDisplayText: '',
      showHintImage: false,
      hintImageUrl: '',
    };

    if (isUpdate.value || isView.value) {
      // 这里应该从API加载现有配置
      // const config = await getProductConfig({ productId });
      // formData.value = { ...config };

      // 临时模拟数据
      formData.value = {
        allowUserAdd: true,
        allowMultiUserAdd: false,
        customDisplayText: '请输入设备码',
        showHintImage: true,
        hintImageUrl: '',
      };
    }

    drawerApi.setState({ confirmLoading: false, loading: false });

    if (view) {
      drawerApi.setState({ showConfirmButton: false });
    } else {
      drawerApi.setState({ showConfirmButton: true });
    }
  },
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true });

    // 这里应该调用API保存配置
    // await saveProductConfig(formData.value);

    console.log('保存配置:', formData.value);
    message.success('配置保存成功');

    emit('reload');
    await handleCancel();
  } catch (error) {
    console.error(error);
    message.error('配置保存失败');
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false });
  }
}

async function handleCancel() {
  drawerApi.close();

  // 重置表单数据
  formData.value = {
    allowUserAdd: false,
    allowMultiUserAdd: false,
    customDisplayText: '',
    showHintImage: false,
    hintImageUrl: '',
  };
}

</script>
