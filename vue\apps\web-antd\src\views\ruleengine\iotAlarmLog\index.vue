<script lang="ts" setup>
import { h, reactive, ref, computed, onMounted } from 'vue';
import { Button, message, Tag, Modal, Popconfirm, Switch } from 'ant-design-vue';
import type { VbenFormProps } from '#/adapter/form';
import type { VxeTableGridOptions, VxeGridListeners } from '#/adapter/vxe-table';
import type { DeepPartial } from '@vben/types';
import { getVxePopupContainer } from '@vben/utils';
import { Page, useVbenModal } from '@vben/common-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { AccessControl, useAccess } from '@vben/access';
const { hasAccessByCodes } = useAccess();
import { commonDownloadExcel } from '#/utils/file/download';
import { List, Export, confirm } from '#/api/ruleengine/iotAlarmLog';
import { MdiPlus, MdiExport, MdiDelete } from '@vben/icons';
import { columns, querySchema, type RowType } from './model';
import editModal from './edit.vue';
import { ListNoPage as ListProduct } from '#/api/device/iotProduct';


const props = defineProps<{
  row?: {
    alarmLogId: string;
    confirmEnable: boolean; // 确保类型正确
    // 其他字段...
  };
}>();

// 操作按钮事件处理
const emit = defineEmits(['delete', 'edit']);

const confirmEnable = ref(true);
async function fetchConfirmEnable() {
  try {
    const response = await List({ page: 1, pageSize: 1 });
    confirmEnable.value = response.data?.confirmEnable !== false;
  } catch (error) {
    console.error('获取confirmEnable参数失败:', error);
  }
}
onMounted(() => {
  fetchConfirmEnable();
});

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  fieldMappingTime: [['date', ['start', 'end']]],
  schema: querySchema,
  // 控制表单是否显示折叠按钮
  showCollapseButton: true,
  // 是否在字段值改变时提交表单
  submitOnChange: true,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};
const gridOptions: VxeTableGridOptions<RowType> = {
  checkboxConfig: {
    highlight: true,
    // labelField: 'alarmLogId',
  },
  rowConfig: {
    keyField: 'alarmLogId',
  },
  columns: columns,
  exportConfig: {},
  height: 'auto',
  keepSource: true,
  showOverflow: false,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await List({
          page: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    export: false,
    refresh: true,
    resizable: true,
    search: true,
    zoom: true,
  },
};
const gridEvents: DeepPartial<VxeGridListeners> = {
  checkboxChange: handleCheckboxChange,
  checkboxAll: handleCheckboxChange,
};
const CheckboxChecked = ref(false);
function handleCheckboxChange() {
  CheckboxChecked.value = gridApi.grid.getCheckboxRecords().length > 0;
}
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents,
});


const [EditModal, editModalApi] = useVbenModal({
  connectedComponent: editModal,
});
function handleAdd() {
  editModalApi.setData({ update: false, view: false });
  editModalApi.open();
}
function handleEdit(row: RowType, confirmState: String) {
  editModalApi.setData({ id: row.alarmLogId, confirmState: confirmState, update: true, view: false });
  editModalApi.open();
  emit('edit', props.row, confirmState);
}
async function handleDelete(row: RowType) {
  emit('delete', props.row);
  try {
    await confirm({
      alarmLogId: row.alarmLogId,
      confirmState: 1, // 设置处理状态为"确认报警"
    });
    message.success('确认报警成功');
    await handleRefresh();
  } catch (error) {
    message.error('确认报警失败');
  }
}

async function autoRefresh() {
  try {
    await gridApi.query();
    console.log('表格数据已自动刷新');
  } catch (error) {
    console.error('自动刷新失败:', error);
  }
}
function handleMultiDelete() {
  const rows = gridApi.grid.getCheckboxRecords();
  const ids = rows.map(row => row.alarmLogId);

  if (ids.length === 0) {
    message.error('请至少选择一项要报警的数据');
    return;
  }

  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认报警选中的${ids.length}条记录吗？`,
    onOk: async () => {
      try {
        await confirm({
          alarmLogId: ids,
          confirmState: 2, // 批量设置处理状态为"确认报警"
        });
        message.success('');
        await handleRefresh();
      } catch (error) {
        message.error('');
      }
    },
  });
}



async function handleExport() {
  const formValues = gridApi.formApi.form.values;
  await commonDownloadExcel(Export, '报警记录表', {
    ...formValues,
    page: 1,
    pageSize: 2000,
  });
  message.success("导出成功");
}

async function handleRefresh() {
  await gridApi.query();
  console.log('表格数据已刷新');
  //throw new Error('Function not implemented.');
}

async function getProductOptions() {
  const res = await ListProduct({});
  if (!res.items) {
    return [];
  }
  const options = res.items.map((item: any) => ({
    label: item.productName,
    value: item.productKey,
  }));
  return options;
}

onMounted(async () => {
  const res = await getProductOptions();
  gridApi.formApi.updateSchema([{
    fieldName: 'productKey',
    component: 'Select',
    label: '报警产品',
    componentProps: {
      placeholder: '请输入报警产品',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: res,
    },
  }]
);
});

</script>
<template>
  <Page auto-content-height>
    <Grid table-title="报警记录">
      <template #toolbar-tools>
      
        <Button class="mr-2 flex items-center" type="primary" :icon="h(MdiExport)" @click="handleExport"
          v-access:code="'cpm:ruleengine:iotAlarmLog:export'">
          导出
        </Button>
      </template>
      <template #action="{ row }">
        <div class="flex items-center" v-if="row.confirmEnable">
          <AccessControl :codes="['cpm:ruleengine:iotAlarmLog:delete']" type="code">
            <Popconfirm title="确认报警吗？" :get-popup-container="getVxePopupContainer" placement="left"
              @confirm="handleDelete(row)">
              <Button class="mr-2 border-none p-0" :block="false" type="link" danger>
                确认报警
              </Button>
            </Popconfirm>
          </AccessControl>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row, '2')"
            v-access:code="'cpm:ruleengine:iotAlarmLog:edit'">
            躁扰
          </Button>
          <Button class="mr-2 border-none p-0" :block="false" type="link" @click="handleEdit(row, '3')"
            v-access:code="'cpm:ruleengine:iotAlarmLog:edit'">
            测试
          </Button>

        </div>
      </template>
    </Grid>
    <EditModal @reload="handleRefresh" @close="editModalApi.close()" />
  </Page>
</template>