
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { DescItem } from '#/components/description';
import { cloneDeep } from 'lodash-es';
import type { VbenFormSchema } from '@vben/common-ui';
import { DictEnum } from '@vben/constants';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';
import { ListNoPage } from '#/api/device/iotDevice';

export class State {
  public alarmLogId = 0; // 报警记录ID
  public alarmConfigId = 0; // 报警配置ID
  public sceneId = 0; // 场景ID
  public productName = ''; // 报警产品
  public deviceName = ''; // 报警设备

  public key = '';
  public value = '';
  public alarmConfigName = '';
  public alarmLevel = 0; // 报警级别
  public startTime = ''; // 开始时间
  public endTime = ''; // 结束时间
  public confirmState = 0; // 处理状态（1=确认报警 2=躁扰报警 3=测试报警）
  public confirmContent = ''; // 处理结果
  public confirmTime = ''; // 处理时间
  public confirmUser = 0; // 处理用户ID
  public confirmUserType = ''; // 报警处理用户（1=设备报警用户 2=终端绑定用户 3=项目接警用户 4=后台用户）
  public sceneConditions = ''; // 场景条件JSON
  public tenantId = ''; // 租户ID（报警配置所属租户）
  public deptId = 0; // 所属机构
  public createdAt = ''; // 创建时间
  public remark = null; // 备注

  constructor(state?: Partial<State>) {
    if (state) {
      Object.assign(this, state);
    }
  }
}

export function newState(state: State | Record<string, any> | null): State {
  if (state !== null) {
    if (state instanceof State) {
      return cloneDeep(state);
    }
    return new State(state);
  }
  return new State();
}

// 表格搜索表单
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'productKey',
    component: 'Select',
    label: '报警产品',
    componentProps: {
      placeholder: '请输入报警产品',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: [],
    },
  },
  {
    fieldName: 'deviceKey',
    component: 'Select',
    label: '报警设备',
    defaultValue: null,
    componentProps: {
      placeholder: '请选择报警设备',
      options: [],
      onUpdateValue: (e: any) => {
        console.log('报警设备变更:', e);
      },
    },
    dependencies: {
      show: () => true,
      componentProps: async (values: any) => {
        const productKey = values.productKey;
        const res = await ListNoPage({ productKey });
        let options = [];
        if(!res || !res.items){
          options = [];
        }else{
          options = res.items.map((item: any) => ({
          label: item.deviceName,
          value: item.deviceKey,
        }));
        }
        return {
        placeholder: '请选择报警设备',
        options: options,
        onUpdateValue: (e: any) => {
          console.log('报警设备变更:', e);
        },
      }},
      triggerFields: ['productKey'],
    },
  },
  {
    fieldName: 'alarmConfigName',
    component: 'Input',
    label: '报警名称',
    componentProps: {
      placeholder: '请输入报警名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  {
    fieldName: 'alarmLevel',
    component: 'Select',
    label: '报警级别',
    componentProps: {
      placeholder: '请选择报警级别',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: getDictOptions(DictEnum.ALARM_LEVEL),
    },
  },
  {
    fieldName: 'createdAt',
    component: 'RangePicker',
    label: '创建时间',
    componentProps: {
      type: 'daterange',
      clearable: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  }
];

// 表格列
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  {
    title: '编号',
    field: 'alarmLogId',
    align: 'center',
    width: 80,
  },
  {
    title: '报警名称',
    field: 'alarmConfigName',
    align: 'left',
    width: -1,
  },
  // {
  //   title: '报警配置ID',
  //   field: 'alarmConfigId',
  //   align: 'left',
  //   width: 100,
  // },
    {
      title: '报警产品',
      field: 'productName',
      align: 'left',
      width: -1,
   },
    {
      title: '报警设备',
      field: 'deviceName',
      align: 'left',
      width: -1,
   },

   {
     title: '报警级别',
     field: 'items.alarmLevel',
     align: 'center',
     width: 100,
     slots: {
       default: ({ row }) => {
       return renderDict(row.alarmLevel, DictEnum.ALARM_LEVEL);
       },
     },
   },
  {
    title: '开始时间',
    field: 'startTime',
    align: 'center',
    width: 180,
  },
  {
    title: '结束时间',
    field: 'endTime',
    align: 'center',
    width: 180,
  },
  {
    title: '处理状态',
    field: 'confirmState',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        return renderDict(row.confirmState, DictEnum.ALARM_CONFIRM_STATE);
      },
    },
  },
  {
    title: '处理用户',
    field: 'confirmUserType',
    align: 'center',
    width: 100,
    slots: {
      default: ({ row }) => {
        if (row.confirmUserType) {
          return renderDict(row.confirmUserType, DictEnum.ALARM_CONFIRM_USER_TYPE);
        }
        return '';
      },
    },
  },
  {
    title: '触发条件',
    field: 'matchFields',
    align: 'left',
    width: 300,
    slots: {
      default: ({ row }) => {
        const matchFields = Array.isArray(row.matchFields) ? row.matchFields : [];
        return matchFields.map((item: any) => {
          return item.key + ': ' + item.value + ' ' + '满足条件: ' + item.relOp + ' ' + item.condVal;
        }).join('\n');
      },
    },
  },
  {
    title: '操作',
    width: 160,
    align: 'center',
    //visible: computed(() => confirmEnable.value), 
    slots: { default: 'action' }
  }
];

// 表格列接口
export interface RowType {
  alarmLogId: number;
  alarmConfigId: number;
  sceneId: number;
  productName: string;
  alarmConfigName: string;
  deviceName: string;
  alarmLevel: number;
  startTime: string;
  endTime: string;
  confirmState: number;
  confirmContent: string;
  confirmTime: string;
  confirmUser: number;
  confirmUserType: string;
  sceneConditions: string;
  tenantId: string;
  deptId: number;
  key: string;
  value: string;
  createdAt: string;
  remark: string;
};

// 查看字段列表
export const viewSchema: DescItem[] = [
  { field: 'alarmLogId', label: '报警记录ID' },
  { field: 'items.alarmConfigId', label: '报警配置ID' },
  { field: 'sceneId', label: '场景ID' },
  { field: 'productName', label: '报警产品' },
  { field: 'deviceName', label: '报警设备' },
  { field: 'items.alarmConfigName', label: '报警名称' },

  { field: 'items.alarmLevel', label: '报警级别' },
  { field: 'startTime', label: '开始时间' },
  { field: 'endTime', label: '结束时间' },
  { field: 'confirmState', label: '处理状态' },
  { field: 'confirmContent', label: '处理结果' },
  { field: 'confirmTime', label: '处理时间' },
  { field: 'confirmUser', label: '处理用户ID' },
  { field: 'confirmUserType', label: '报警处理用户' },
  { field: 'sceneConditions', label: '场景条件JSON' },
  { field: 'deptId', label: '所属机构' },
  { field: 'createdAt', label: '创建时间' },
  { field: 'remark', label: '备注' },
];

// 编辑字段列表
export const editSchema: VbenFormSchema[] = [
  {
    fieldName: 'alarmLogId',
    component: 'Input',
    label: '报警记录ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  //   {
  //     fieldName: 'items.alarmConfigId',
  //     component: 'InputNumber',
  //     label: '报警配置ID',
  //     componentProps: {
  //       placeholder: '请输入报警配置ID',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:z.number({required_error: '请输入报警配置ID', invalid_type_error: '无效数字'})
  // },
  //   {
  //     fieldName: 'sceneId',
  //     component: 'InputNumber',
  //     label: '场景ID',
  //     componentProps: {
  //       placeholder: '请输入场景ID',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:z.number({required_error: '请输入场景ID', invalid_type_error: '无效数字'})
  // },
  //   {
  //     fieldName: 'productName',
  //     component: 'Input',
  //     label: '报警产品',
  //     componentProps: {
  //       placeholder: '请输入报警产品',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:'required'
  // },
  //   {
  //     fieldName: 'deviceName',
  //     component: 'Input',
  //     label: '报警设备',
  //     componentProps: {
  //       placeholder: '请输入报警设备',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:'required'
  // },

  //   {
  //     fieldName: ' items.alarmConfigName',
  //     component: 'Input',
  //     label: '报警名称',
  //     componentProps: {
  //       placeholder: '请输入报警名称',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:'required'
  // },
  //   {
  //     fieldName: 'items.alarmLevel',
  //     component: 'InputNumber',
  //     label: '报警级别',
  //     componentProps: {
  //       placeholder: '请输入报警级别',
  //       options: getDictOptions('alarm_level'),
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:z.number({required_error: '请输入报警级别', invalid_type_error: '无效数字'})
  // },
  //   {
  //     fieldName: 'startTime',
  //     component: 'DatePicker',
  //     label: '开始时间',
  //     componentProps: {
  //       type: 'datetime',
  //       clearable: true,
  //       shortcuts: 'FMTime',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:'selectRequired'
  // },
  //   {
  //     fieldName: 'endTime',
  //     component: 'DatePicker',
  //     label: '结束时间',
  //     componentProps: {
  //       type: 'datetime',
  //       clearable: true,
  //       shortcuts: 'FMTime',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:null
  // },
  {
    fieldName: 'confirmState',
    component: 'Select',
    label: '处理状态',
    componentProps: {
      placeholder: '请输入处理状态（1=确认报警 2=躁扰报警 3=测试报警）',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
      options: getDictOptions(DictEnum.ALARM_CONFIRM_STATE),

    },
    disabled: true,
    // rules:z.number({required_error: '请输入处理状态（1=确认报警 2=躁扰报警 3=测试报警）', invalid_type_error: '无效数字'})
  },
  {
    fieldName: 'confirmContent',
    component: 'Textarea',
    label: '处理结果',
    componentProps: {
      placeholder: '请输入处理结果',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  //   {
  //     fieldName: 'confirmTime',
  //     component: 'DatePicker',
  //     label: '处理时间',
  //     componentProps: {
  //       type: 'datetime',
  //       clearable: true,
  //       shortcuts: 'FMTime',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:null
  // },
  //   {
  //     fieldName: 'confirmUser',
  //     component: 'InputNumber',
  //     label: '处理用户ID',
  //     componentProps: {
  //       placeholder: '请输入处理用户ID',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:null
  // },
  //   {
  //     fieldName: 'confirmUserType',
  //     component: 'Input',
  //     label: '报警处理用户（1=设备报警用户 2=终端绑定用户 3=项目接警用户 4=后台用户）',
  //     componentProps: {
  //       placeholder: '请输入报警处理用户（1=设备报警用户 2=终端绑定用户 3=项目接警用户 4=后台用户）',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:'required'
  // },
  //   {
  //     fieldName: 'sceneConditions',
  //     component: 'Input',
  //     label: '场景条件JSON',
  //     componentProps: {
  //       placeholder: '请输入场景条件JSON',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:'required'
  // },
  //   {
  //     fieldName: 'tenantId',
  //     component: 'Input',
  //     label: '租户ID（报警配置所属租户）',
  //     componentProps: {
  //       placeholder: '请输入租户ID（报警配置所属租户）',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:'required'
  // },
  //   {
  //     fieldName: 'deptId',
  //     component: 'InputNumber',
  //     label: '所属机构',
  //     componentProps: {
  //       placeholder: '请输入所属机构',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:z.number({required_error: '请输入所属机构', invalid_type_error: '无效数字'})
  // },
  //   {
  //     fieldName: 'remark',
  //     component: 'Input',
  //     label: '备注',
  //     componentProps: {
  //       placeholder: '请输入备注',
  //       onUpdateValue: (e: any) => {
  //         console.log(e);
  //       },
  //     },
  //   rules:null
  // },
];
// export const editSchema2: VbenFormSchema[] = [
//   {
//     fieldName: 'alarmLogId',
//     component: 'Input',
//     label: '报警记录ID',
//     dependencies: {   show: () => false,    triggerFields: [''],   },
//     componentProps: {
//       placeholder: '',
//       onUpdateValue: (e: any) => {
//         console.log(e);
//       },
//     },
//   },
// //   {
// //     fieldName: 'items.alarmConfigId',
// //     component: 'InputNumber',
// //     label: '报警配置ID',
// //     componentProps: {
// //       placeholder: '请输入报警配置ID',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:z.number({required_error: '请输入报警配置ID', invalid_type_error: '无效数字'})
// // },
// //   {
// //     fieldName: 'sceneId',
// //     component: 'InputNumber',
// //     label: '场景ID',
// //     componentProps: {
// //       placeholder: '请输入场景ID',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:z.number({required_error: '请输入场景ID', invalid_type_error: '无效数字'})
// // },
// //   {
// //     fieldName: 'productName',
// //     component: 'Input',
// //     label: '报警产品',
// //     componentProps: {
// //       placeholder: '请输入报警产品',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:'required'
// // },
// //   {
// //     fieldName: 'deviceName',
// //     component: 'Input',
// //     label: '报警设备',
// //     componentProps: {
// //       placeholder: '请输入报警设备',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:'required'
// // },

// //   {
// //     fieldName: ' items.alarmConfigName',
// //     component: 'Input',
// //     label: '报警名称',
// //     componentProps: {
// //       placeholder: '请输入报警名称',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:'required'
// // },
// //   {
// //     fieldName: 'items.alarmLevel',
// //     component: 'InputNumber',
// //     label: '报警级别',
// //     componentProps: {
// //       placeholder: '请输入报警级别',
// //       options: getDictOptions('alarm_level'),
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:z.number({required_error: '请输入报警级别', invalid_type_error: '无效数字'})
// // },
// //   {
// //     fieldName: 'startTime',
// //     component: 'DatePicker',
// //     label: '开始时间',
// //     componentProps: {
// //       type: 'datetime',
// //       clearable: true,
// //       shortcuts: 'FMTime',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:'selectRequired'
// // },
// //   {
// //     fieldName: 'endTime',
// //     component: 'DatePicker',
// //     label: '结束时间',
// //     componentProps: {
// //       type: 'datetime',
// //       clearable: true,
// //       shortcuts: 'FMTime',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:null
// // },
//   {
//     fieldName: 'confirmState',
//     component: 'Input',
//     label: '处理状态：  躁扰报警',
//   //   componentProps: {
//   //     placeholder: '请输入处理状态（1=确认报警 2=躁扰报警 3=测试报警）',
//   //     onUpdateValue: (e: any) => {
//   //       console.log(e);
//   //     },
//   //   },
//   // rules:z.number({required_error: '请输入处理状态（1=确认报警 2=躁扰报警 3=测试报警）', invalid_type_error: '无效数字'})
// },
//   {
//     fieldName: 'confirmContent',
//     component: 'Input',
//     label: '处理结果',
//     componentProps: {
//       placeholder: '请输入处理结果',
//       onUpdateValue: (e: any) => {
//         console.log(e);
//       },
//     },
//   rules:'required'
// },
// //   {
// //     fieldName: 'confirmTime',
// //     component: 'DatePicker',
// //     label: '处理时间',
// //     componentProps: {
// //       type: 'datetime',
// //       clearable: true,
// //       shortcuts: 'FMTime',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:null
// // },
// //   {
// //     fieldName: 'confirmUser',
// //     component: 'InputNumber',
// //     label: '处理用户ID',
// //     componentProps: {
// //       placeholder: '请输入处理用户ID',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:null
// // },
// //   {
// //     fieldName: 'confirmUserType',
// //     component: 'Input',
// //     label: '报警处理用户（1=设备报警用户 2=终端绑定用户 3=项目接警用户 4=后台用户）',
// //     componentProps: {
// //       placeholder: '请输入报警处理用户（1=设备报警用户 2=终端绑定用户 3=项目接警用户 4=后台用户）',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:'required'
// // },
// //   {
// //     fieldName: 'sceneConditions',
// //     component: 'Input',
// //     label: '场景条件JSON',
// //     componentProps: {
// //       placeholder: '请输入场景条件JSON',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:'required'
// // },
// //   {
// //     fieldName: 'tenantId',
// //     component: 'Input',
// //     label: '租户ID（报警配置所属租户）',
// //     componentProps: {
// //       placeholder: '请输入租户ID（报警配置所属租户）',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:'required'
// // },
// //   {
// //     fieldName: 'deptId',
// //     component: 'InputNumber',
// //     label: '所属机构',
// //     componentProps: {
// //       placeholder: '请输入所属机构',
// //       onUpdateValue: (e: any) => {
// //         console.log(e);
// //       },
// //     },
// //   rules:z.number({required_error: '请输入所属机构', invalid_type_error: '无效数字'})
// // },

//   {
//     fieldName: 'remark',
//     component: 'Input',
//     label: '备注',
//     componentProps: {
//       placeholder: '请输入备注',
//       onUpdateValue: (e: any) => {
//         console.log(e);
//       },
//     },
//   rules:null
// },
// ];

// function rangePickerRequired(data: any) {
//   throw new Error('Function not implemented.');
// }
