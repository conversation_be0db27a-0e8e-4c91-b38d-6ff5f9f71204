import type { VbenFormSchema } from "@vben/common-ui";
import type { VxeGridProps } from "@vben/plugins/vxe-table";

/** 表格搜索 */
export const querySchema: VbenFormSchema[] = [
  {
    fieldName: 'unionKey',
    component: 'Input',
    label: '变量名称',
    componentProps: {
      placeholder: '请输入变量名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  }, {
    fieldName: 'unionKey',
    component: 'Input',
    label: '变量标识',
    componentProps: {
      placeholder: '请输入变量标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: null
  },
]

/** 表格列配置 */
export const columns: VxeGridProps['columns'] = [
  {
    type: 'checkbox',
    width: 40,
  },
  // 组合设备ID
  {
    title: '编号',
    field: 'unionId',
    align: 'center',
    width: 80,
  },
  // 组合设备名称
  {
    title: '组合设备名称',
    field: 'unionName',
    align: 'center',
    width: -1,
  },
  //  组合设备标识
  {
    title: '组合设备标识',
    field: 'unionKey',
    align: 'center',
    width: -1,
  },
  // 属性单位
  {
    title: '属性单位',
    field: 'remark',
    align: 'center',
    width: -1,
  },
  // 属性类型
  {
    title: '属性类型',
    field: 'remark',
    align: 'center',
    width: -1,
  },
  // 属性默认值
  {
    title: '属性默认值',
    field: 'remark',
    align: 'center',
    width: -1,
  },
  // 是否启用
  {
    title: '是否启用',
    field: 'status',
    align: 'center',
    width: 80,
    slots: { default: 'status' },
  },
  { title: '操作', width: 120, slots: { default: 'action' } },
];


/** 新增/编辑表单 */
export const editSchema: VbenFormSchema[] = [
  /** 属性ID */
  {
    fieldName: 'unionId',
    component: 'Input',
    label: '属性ID',
    dependencies: { show: () => false, triggerFields: [''], },
    componentProps: {
      placeholder: '',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  /** 属性名称 */
  {
    fieldName: 'unionName',
    component: 'Input',
    label: '属性名称',
    componentProps: {
      placeholder: '请输入属性名称',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  /** 属性标识 */
  {
    fieldName: 'unionKey',
    component: 'Input',
    label: '属性标识',
    componentProps: {
      placeholder: '请输入属性标识',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  /** 属性单位 */
  {
    fieldName: 'unionKey',
    component: 'Input',
    label: '属性单位',
    componentProps: {
      placeholder: '请输入属性单位',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'required'
  },
  /** 计算周期 */
  {
    fieldName: 'computed',
    component: 'Input',
    label: '计算周期',
    rules: 'required'
  },
  /** 属性默认值 */
  {
    fieldName: 'unionKey',
    component: 'Input',
    label: '变量及计算公式',
    componentProps: {
      placeholder: '请输入属性默认值',
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
  },
  /** 是否历史存储 */
  {
    fieldName: 'status',
    component: 'Switch',
    label: '是否历史存储',
    defaultValue: false,
    componentProps: {
      style: {
        width: '20px',
      },
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired',
  },
  /** 是否启用 */
  {
    fieldName: 'status',
    component: 'Switch',
    label: '是否启用',
    defaultValue: false,
    componentProps: {
      style: {
        width: '20px',
      },
      onUpdateValue: (e: any) => {
        console.log(e);
      },
    },
    rules: 'selectRequired',
  },
];