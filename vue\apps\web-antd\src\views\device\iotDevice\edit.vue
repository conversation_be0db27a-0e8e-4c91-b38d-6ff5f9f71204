<template>
  <BasicDrawer :close-on-click-modal="false" :title="title" class="w-[700px]">
    <BasicForm>
      <template #deviceLocationPicker="slotProps">
        <div style="display: flex; gap: 8px;">
          <Button type="primary" @click="openSelectLocation">地图选点</Button>
        </div>
      </template>
      <template #tags="slotProps">
        <div>
          <div v-for="(item, index) in slotProps.value" style="display: flex; align-items: center;">
            <Input type="text" :addon-before="item.tagName" :value="item.tagValue" readonly />
            <Button @click="handleEditTag(index)">编辑</Button>
            <Button @click="handleDeleteTag(index)">删除</Button>
          </div>
          <Button type="primary" @click="handleAddTagClick">添加标签</Button>
        </div>
      </template>
      <template #imgUrl="slotProps">
        <ImageUpload v-bind="slotProps" :accept="accept" :max-number=1
          :api="(file, progressEvent) => uploadApi(file, progressEvent, true)" />
      </template>

    </BasicForm>
  </BasicDrawer>
  <TagModal class="w-[600px] h-[400px]" @confirm="handleTagSelect" />
  <SelectLocation ref="refSelectLocation" @locationSelected="onLocationSelected" />
</template>
<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { Button, Input } from 'ant-design-vue';
import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';
import { ImageUpload } from '#/components/upload';
import { View as getProductDetail, ListNoPage as ListProduct } from '#/api/device/iotProduct'; // 获取产品详情
import { GB28181Type } from '#/api/ruleengine/iotChannel'; // 获取GB28181类型

import { useVbenForm } from '#/adapter/form';
import { Edit, View } from '#/api/device/iotDevice';
import { editSchema } from './model';
import editTagModal from '../iotDeviceTags/edit.vue';
import { type RowType as TagRowType } from '../iotDeviceTags/model';
import { uploadApi } from '#/api';
import SelectLocation from './selectLocation/index.vue';
import { z } from '@vben/common-ui';

const emit = defineEmits<{ reload: [] }>();
interface ModalProps {
  id?: number | string;
  update: boolean;
  view: boolean;
  productOptions: any[];
}
const accept = ref(['jpg', 'jpeg', 'png', 'gif', 'webp']);


const isUpdate = ref(false);
const isView = ref(false);
const proOptions = ref<any[]>([]);
const originalProOptions = ref<any[]>([]); // 保存原始的产品选项
const currentDeviceIsMonitor = ref(false); // 当前设备是否为监控设备

// 加载完整的产品信息（包含deviceType）
async function loadProductOptionsWithDeviceType() {
  try {
    const res = await ListProduct({
      page: 1,
      pageSize: 1000,
    });

    if (res && res.items) {
      // 返回包含完整产品信息的选项
      return res.items.map((item: any) => ({
        label: item.productName,
        value: item.productKey,
        deviceType: item.deviceType, // 包含设备类型
        ...item // 包含所有产品信息
      }));
    }
    return [];
  } catch (error) {
    console.error('加载产品选项失败:', error);
    return [];
  }
}

// 用于跟踪用户是否修改了地图相关字段
const userModifiedMapData = ref<{
  longitude?: number;
  latitude?: number;
  networkAddress?: string;
} | null>(null);
const title = computed(() => {
  if (isView.value) {
    return $t('pages.common.view');
  }
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'col-span-4',
  },
  layout: 'vertical',
  schema: editSchema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-4 gap-x-4',
});

// 添加缺失的变量声明
const product = ref<any>({}); // 缺失的product变量

// 添加缺失的drawer和modal API声明
const [BasicDrawer, drawerApi] = useVbenDrawer({
  onOpenChange,
  onConfirm: handleConfirm,
  onCancel: handleCancel,
});

// 监控设备相关常量
const MONITOR_DEVICE_TYPE = 4;
const QINIU_GB28181_TYPE = 'qiniu';

// 监控设备检测辅助函数
function isMonitorDevice(deviceType: number | string): boolean {
  return deviceType === MONITOR_DEVICE_TYPE || deviceType === String(MONITOR_DEVICE_TYPE);
}

// 设备标识字段配置
interface DeviceKeyConfig {
  placeholder: string;
  readonly: boolean;
  disabled: boolean;
  rules: string | z.ZodTypeAny;
}

function getDeviceKeyConfig(isReadonly: boolean): DeviceKeyConfig {
  if (isReadonly) {
    return {
      placeholder: '设备标识保存时自动生成',
      readonly: true,
      disabled: true,
      rules: '', // 移除必填规则
    };
  }

  return {
    placeholder: '请输入设备标识',
    readonly: false,
    disabled: false,
    rules: z.string()
      .min(1, '设备标识不能为空')
      .max(32, '最大长度32')  
      .regex(/^[a-zA-Z_][a-zA-Z0-9_]*$/, '必须是字母数字下划线或组成，且不能以数字开头'),
  };
}

// 更新设备标识字段
function updateDeviceKeyField(config: DeviceKeyConfig) {
  formApi.updateSchema([
    {
      fieldName: 'deviceKey',
      component: 'Input',
      label: '设备标识',
      componentProps: {
        placeholder: config.placeholder,
        readonly: config.readonly,
        disabled: config.disabled,
      },
      formItemClass: 'col-span-2',
      rules: config.rules,
    }
  ]);
}

// 检查GB28181类型是否为七牛
async function checkIsQiniuGB28181(): Promise<boolean> {
  try {
    const gb28181Result = await GB28181Type({});
    return gb28181Result && gb28181Result.type === QINIU_GB28181_TYPE;
  } catch (error) {
    console.error('调用GB28181Type接口失败:', error);
    return false;
  }
}

// 处理监控设备的设备标识字段
async function handleMonitorDeviceKey(): Promise<boolean> {

  const isQiniuType = await checkIsQiniuGB28181();

  if (isQiniuType) {
    const config = getDeviceKeyConfig(true);
    updateDeviceKeyField(config);
    return true; // 返回true表示设备标识为只读
  } else {
    const config = getDeviceKeyConfig(false);
    updateDeviceKeyField(config);
    return false; // 返回false表示设备标识可编辑
  }
}

// 处理非监控设备的设备标识字段
function handleNormalDeviceKey() {
  const config = getDeviceKeyConfig(false);
  updateDeviceKeyField(config);
}

// 重构后的updateDevicekeySchema函数
function updateDevicekeySchema(deviceKeyDisable: boolean) {
  const config = getDeviceKeyConfig(deviceKeyDisable);
  updateDeviceKeyField(config);
}

// 处理产品选择变化的核心逻辑
async function handleProductChange(productKey: string) {
  try {
    console.log('🔄 产品切换 - 选择的产品Key:', productKey);

    // 获取产品详情
    product.value = await getProductDetail({ productKey });
    console.log('📦 获取到的产品详情:', product.value);

    // 统一设置产品相关字段（包括 deviceModel）
    await setProductFields(product.value);

    // 处理设备标识字段
    await handleDeviceKeyByProductType(product.value);

    // 输出当前表单值用于调试
    const values = await formApi.getValues();
    console.log('📝 当前表单值:', values);
    console.log('🏷️ 设备型号字段值:', values.deviceModel);
  } catch (error) {
    console.error('处理产品变化时发生错误:', error);
  }
}

// 修改 setProductFields 函数，添加 deviceModel 字段的处理
async function setProductFields(productInfo: any) {
  if (!productInfo) {
    await formApi.setValues({
      imgUrl: '',
      onlineTimeout: 300, // 设置默认值
      deviceModel: '' // 清空设备型号
    });
    return;
  }

  const updateData: any = {};

  // 获取当前表单中的 onlineTimeout 值
  const currentValues = await formApi.getValues();
  const currentTimeout = currentValues.onlineTimeout;

  // 只有在用户没有输入值时才使用产品的超时时间
  if (!currentTimeout || currentTimeout <= 0) {
    if (productInfo.onlineTimeout && productInfo.onlineTimeout > 0) {
      updateData.onlineTimeout = productInfo.onlineTimeout;
    } else {
      updateData.onlineTimeout = 300; // 默认5分钟
    }
  }

  // 设置产品图片
  if (productInfo.imgUrl) {
    updateData.imgUrl = productInfo.imgUrl;
  } else {
    updateData.imgUrl = '';
  }

  // 设置设备默认型号 - 从产品信息中获取
  if (productInfo.deviceModel) {
    updateData.deviceModel = productInfo.deviceModel;
    console.log('🔧 设置设备默认型号:', productInfo.deviceModel);
  } else {
    updateData.deviceModel = '';
    console.log('🔧 产品没有设置默认型号，清空字段');
  }

  await formApi.setValues(updateData);
}

// 根据产品类型处理设备标识字段
async function handleDeviceKeyByProductType(productInfo: any) {
  const deviceIsMonitor = isMonitorDevice(productInfo.deviceType);

  if (deviceIsMonitor) {
    const isReadonly = await handleMonitorDeviceKey();

    // 如果是新增模式且设备标识为只读，清空设备标识字段
    if (!isUpdate.value && isReadonly) {
      await formApi.setValues({ deviceKey: '' });
    }
  } else {
    handleNormalDeviceKey();
  }
}

// 获取过滤后的产品选项
function getFilteredProductOptions(allProducts: any[], isCurrentDeviceMonitor: boolean): any[] {
  if (isCurrentDeviceMonitor) {
    return allProducts.filter(product => isMonitorDevice(product.deviceType));
  } else {
    return allProducts;
  }
}

// 初始化设备标识字段（新增模式）
function initializeDeviceKeyForAdd() {
  const config = getDeviceKeyConfig(false);
  updateDeviceKeyField(config);
}

// 处理编辑模式的产品选项和设备标识字段
async function handleEditModeInitialization(allProducts: any[], deviceRecord: any) {
  if (!deviceRecord.productKey) {
    proOptions.value = allProducts;
    return;
  }

  // 获取当前设备的产品信息
  const currentProduct = allProducts.find((p: any) => p.value === deviceRecord.productKey);

  // 判断当前设备是否为监控设备
  currentDeviceIsMonitor.value = currentProduct && isMonitorDevice(currentProduct.deviceType);

  // 设置产品选项
  proOptions.value = getFilteredProductOptions(allProducts, currentDeviceIsMonitor.value);

  // 更新设备标识字段
  updateDevicekeySchema(currentDeviceIsMonitor.value);
}

// 修改原有的onChange函数
const createProductChangeHandler = () => async (productKey: string) => {
  await handleProductChange(productKey);
};

// 在onOpenChange中的使用
async function onOpenChange(isOpen: boolean) {
  if (!isOpen) {
    return null;
  }
  console.log('iotDevice/edit.vue onOpenChange begin');
  drawerApi.setState({ confirmLoading: true, loading: true });

  const { id, update, view, productOptions } = drawerApi.getData() as ModalProps;
  isUpdate.value = update;
  isView.value = view;

  // 加载完整的产品信息
  const allProducts = await loadProductOptionsWithDeviceType();
  originalProOptions.value = allProducts;

  if (isUpdate.value && id) {
    // 编辑模式的处理保持不变
    const record = await View({ deviceId: id });
    await handleEditModeInitialization(allProducts, record);

    record.isShadow = String(record.isShadow);

    if (record.imgUrl && typeof record.imgUrl === 'string') {
      record.imgUrl = record.imgUrl;
      record.imgUrl = record.imgUrl;
    }

    await formApi.setValues(record);
  } else {
    // 新增模式 - 清空所有产品相关字段
    proOptions.value = allProducts;
    initializeDeviceKeyForAdd();

    // 清空产品相关字段，包括设备型号
    await formApi.setValues({
      imgUrl: '',
      onlineTimeout: undefined,
      productKey: undefined,
      deviceModel: '' // 清空设备型号字段
    });
  }

  drawerApi.setState({ confirmLoading: false, loading: false });

  // 设置表单状态和产品选择监听
  if (view) {
    drawerApi.setState({ showConfirmButton: false });
    formApi.setState({
      commonConfig: {
        componentProps: {
          readonly: true,
          "only-read": true,
        }
      }
    });
  } else {
    drawerApi.setState({ showConfirmButton: true });

    // 重点：监听所属产品变化
    formApi.updateSchema([
      {
        fieldName: 'productKey',
        component: 'Select',
        label: '所属产品',
        componentProps: {
          placeholder: '请选择所属产品',
          options: proOptions.value,
          onChange: createProductChangeHandler(),
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          }
        },
      }
    ]);

    formApi.setState({
      commonConfig: {
        componentProps: {
          readonly: false,
          "only-read": false,
        }
      }
    });
  }
}

// 更新onMounted中的逻辑
onMounted(() => {
  // 只设置产品选择的监听器，不处理具体的产品字段
  console.log('iotDevice/edit.vue onMounted begin');
  formApi.updateSchema([
    {
      fieldName: 'productKey',
      component: 'Select',
      label: '所属产品',
      componentProps: {
        placeholder: '请选择所属产品',
        options: proOptions.value,
        onChange: createProductChangeHandler(), // 使用统一的处理函数
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        }
      },
    }
  ]);
  console.log('iotDevice/edit.vue onMounted end');
});

async function handleConfirm() {
  try {
    drawerApi.setState({ confirmLoading: true, loading: true })

    const { valid } = await formApi.validate();
    if (!valid) {
      console.warn('❌ 表单验证失败');
      return;
    }

    const data = cloneDeep(await formApi.getValues());

    // 修复 onlineTimeout 处理逻辑
    data.onlineTimeout = data.onlineTimeout || 0;

    // 确保 onlineTimeout 字段存在且有效
    if (!data.onlineTimeout || data.onlineTimeout <= 0) {
      // 优先使用产品的超时时间，如果产品没有则使用默认值
      if (product.value && product.value.onlineTimeout && product.value.onlineTimeout > 0) {
        data.onlineTimeout = product.value.onlineTimeout;
      } else {
        data.onlineTimeout = 300; // 默认5分钟
      }
    } else {
      // 用户已经输入了有效值，保持用户的输入
    }
    // 标准化图片URL格式
    if (data.imgUrl && typeof data.imgUrl === 'string') {
      if (data.imgUrl.startsWith('/upload/')) {
        data.imgUrl = data.imgUrl;
      } else if (data.imgUrl.startsWith('app/upload/')) {
        data.imgUrl = data.imgUrl.replace('app/upload/', '/upload/');
      }
    }

    // 地图相关字段处理
    console.log('🗺️ 地图字段检查:', {
      longitude: data.longitude,
      latitude: data.latitude,
      networkAddress: data.networkAddress
    });

    if (userModifiedMapData.value) {
      console.log('🗺️ 检测到用户修改了地图数据，使用用户的选择');
      data.longitude = userModifiedMapData.value.longitude;
      data.latitude = userModifiedMapData.value.latitude;

      if (userModifiedMapData.value.networkAddress) {
        data.networkAddress = userModifiedMapData.value.networkAddress;
      }
    }

    // 确保包含必要的ID字段用于编辑
    const drawerData = drawerApi.getData() as ModalProps;
    if (isUpdate.value && drawerData.id && !data.deviceId) {
      data.deviceId = drawerData.id;
      console.log('🔧 补充设备ID:', data.deviceId);
    }


    console.log('📤 最终提交的数据:', data);

    // 再次确认 onlineTimeout 字段
    console.log('📤 提交前最终检查 onlineTimeout:', {
      value: data.onlineTimeout,
      type: typeof data.onlineTimeout,
      isValid: data.onlineTimeout && data.onlineTimeout > 0
    });

    try {
      const result = await Edit(data);
      console.log('✅ Edit API 调用成功:', result);

      emit('reload');
      userModifiedMapData.value = null;
      await handleCancel();
      console.log('✅ 设备编辑完成');
    } catch (apiError) {
      console.error('❌ Edit API 调用失败:', apiError);
      if (apiError && typeof apiError === 'object') {
        console.error('错误详情:', {
          message: (apiError as any).message,
          status: (apiError as any).status,
          response: (apiError as any).response
        });
      }
      throw apiError;
    }
  } catch (error) {
    console.error('❌ 设备编辑过程中发生错误:', error);
  } finally {
    drawerApi.setState({ confirmLoading: false, loading: false })
  }
}

async function handleCancel() {
  // 清理用户修改的地图数据
  userModifiedMapData.value = null;
  drawerApi.close();
  await formApi.resetForm();
}

const [TagModal, TagModalApi] = useVbenModal({
  connectedComponent: editTagModal
})

// 地图选点相关
const refSelectLocation = ref();

async function handleAddTagClick() {
  await TagModalApi.setData({ update: false, index: 0, tagData: null })
  TagModalApi.open();
}
async function handleEditTag(index: number) {
  const values = await formApi.getValues();
  const tags = values.tags || [];
  const tag = tags[index];
  await TagModalApi.setData({ update: true, index: index, tagData: tag });
  TagModalApi.open();
}

async function handleDeleteTag(index: number) {
  const values = await formApi.getValues();
  const tags = values.tags || [];
  const newTags = tags.filter((_: any, i: number) => i !== index);
  await formApi.setValues({
    tags: newTags,
  })
}

async function handleTagSelect(update: boolean, index: number, tagData: TagRowType) {
  const values = await formApi.getValues();
  let newTags = values.tags ? [...values.tags] : [];
  if (!update) {
    newTags = [...newTags, tagData];
  }
  if (update) {
    newTags.forEach((_: any, i: number) => {
      if (i === index) {
        newTags[i] = tagData;
      }
    });
  }
  await formApi.setValues({
    tags: newTags,
  })
}

// 地图选点相关方法
async function openSelectLocation() {
  // 获取表单中当前的经纬度值
  const values = await formApi.getValues();
  const lng = values.longitude;
  const lat = values.latitude;

  console.log('表单中的经纬度:', lng, lat);

  // 传递经纬度给弹窗
  refSelectLocation.value.openModal(lng, lat);
}

// 位置选择弹窗回调
async function onLocationSelected(lng: number, lat: number, address?: string) {
  console.log('🗺️ 地图选点回调 - 选择的位置:', lng, lat, '地址:', address);
  console.log('🗺️ 地址参数类型和值:', typeof address, address);

  // 保存用户修改的地图数据
  userModifiedMapData.value = {
    longitude: lng,
    latitude: lat,
    networkAddress: address || undefined // 确保空字符串不被保存
  };

  console.log('🗺️ 保存用户修改的地图数据:', userModifiedMapData.value);

  // 更新表单中的经纬度值和地址
  const updateData: any = {
    longitude: lng,
    latitude: lat
  };

  // 如果有地址信息，同时更新设备地址字段
  if (address) {
    updateData.networkAddress = address;
  }

  console.log('🗺️ 准备更新表单数据:', updateData);

  // 先尝试单独设置地址字段
  if (address) {
    console.log('🗺️ 单独设置地址字段:', address);
    await formApi.setValues({ networkAddress: address });

    // 验证单独设置是否成功
    const checkValues = await formApi.getValues();
    console.log('🗺️ 单独设置地址后的验证:', checkValues.networkAddress);
  }

  await formApi.setValues(updateData);

  // 验证数据是否正确设置
  const currentValues = await formApi.getValues();
  console.log('🗺️ 表单更新后的完整数据:', currentValues);
  console.log('🗺️ 经纬度字段验证:', {
    longitude: currentValues.longitude,
    latitude: currentValues.latitude,
    networkAddress: currentValues.networkAddress
  });
}

</script>
